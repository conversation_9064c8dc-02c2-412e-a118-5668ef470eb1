
# 项目需求文档 (PRD) - RPA 自动化平台

## 1. 项目概述

### 1.1. 项目目标

旨在通过RPA（机器人流程自动化）技术，实现对现有管理后台的自动化操作，从而替代人工，提升效率、降低成本。本项目的核心是构建一个由工单系统和AI助手组成的闭环自动化解决方案，免去后台系统API的开发。

### 1.2. 系统组成

本项目包含三大系统：

1.  **工单系统 (需开发)**: 用户提交、管理和追踪自动化任务的平台。
2.  **AI 助手 (需开发)**: 负责理解工单内容、拆解任务、操作浏览器以及更新工单状态的核心自动化引擎。
3.  **管理后台 (已有)**: AI 助手进行自动化操作的目标业务系统。

### 1.3. 业务流程

```mermaid
graph TD
    A[用户在工单系统提交工单] --> B{AI 助手获取工单};
    B --> C{AI 助手操作管理后台处理工单};
    C --> D[AI 助手更新工单状态];
    D --> A;
```

---

## 2. 核心系统

### 2.1. 工单系统

#### 2.1.1. 功能需求

##### 2.1.1.1. 工单列表页

| 模块 | 功能/字段 | 描述 |
| :--- | :--- | :--- |
| **筛选** | 工单号 | 支持部分匹配查询。 |
| | 内容 | 支持对工单内容进行模糊搜索。 |
| | 状态 | 支持按工单状态（待开始、处理中、待补充信息、已完成、已挂起）进行筛选。 |
| **操作** | 新建工单 | 点击后弹出新建工单窗口。 |
| | 批量设置状态 | 允许用户选择多个工单并统一变更其状态。 |
| **列表字段** | 序号 | 列表的顺序编号。 |
| | 工单号 | 唯一标识一个工单。 |
| | 工单内容 | 仅显示一行的摘要信息。 |
| | 工单状态 | 当前工单的处理状态。 |
| | 备注 | 仅当需要补充信息时显示，展示最新的补充请求。 |
| | 待办任务 | 显示任务总数和已完成数（例如 "3/5"）。 |
| | 处理时间 | 已完成的工单显示创建到完成的时间差；未完成的显示创建到当前的时间差（单位：小时）。 |
| | 创建日期 | 工单的创建时间。 |
| | 更新日期 | 工单最后一次更新的时间。 |
| | 操作 | 查看、编辑、设置状态、删除（仅"待开始"、"已挂起"、"已完成"状态的工单可删除）。 |

##### 2.1.1.2. 新建/编辑/查看工单详情页 (弹窗)

| 字段/功能 | 描述 |
| :--- | :--- |
| **工单号** | 仅在编辑和查看时显示。 |
| **工单状态** | 下拉选择工单状态。 |
| **内容** | 支持Markdown语法的富文本编辑器。 |
| **备注** | 由AI助手填写，用户只读。记录需要用户补充的信息，包含日期和是否已补充的标识。 |
| **完单总结** | 工单完成后由AI助手生成，用户只读。 |
| **任务详情** | 展示任务列表、处理进度、已完成任务的截图和小结，由AI助手更新，用户只读。 |
| **功能按钮** | 保存、取消。在查看模式下，仅显示“关闭”按钮。 |

#### 2.1.2. 工单状态流转

```mermaid
stateDiagram-v2
    [*] --> 待开始
    待开始 --> 已挂起: 用户操作
    待开始 --> 处理中: AI助手处理
    处理中 --> 已挂起: 用户操作
    处理中 --> 待补充信息: AI助手操作
    处理中 --> 已完成: AI助手处理完成
    待补充信息 --> 已挂起: 用户操作
    待补充信息 --> 待开始: 用户补充信息后
    已挂起 --> 待开始: 用户操作
```

| 状态 | 描述 | 用户可执行操作 |
| :--- | :--- | :--- |
| **待开始** | 新建工单或用户补充信息后。 | 查看、编辑、删除、更改为“已挂起”。 |
| **处理中** | AI 助手正在处理。 | 查看、更改为“已挂起”（需二次确认）。 |
| **待补充信息** | AI 助手需要更多信息。 | 查看、编辑、更改为“已挂起”。 |
| **已完成** | AI 助手已完成所有任务。 | 查看、删除。 |
| **已挂起** | 用户暂停工单。 | 查看、编辑、删除、更改为“待开始”。 |

---

### 2.2. AI 助手

#### 2.2.1. 核心功能

1.  **指令获取**: 从工单系统顺序获取待处理的工单，一次处理一个。
2.  **任务构建**:
    *   理解多模态指令（文本+图片）。
    *   如果信息不足，更新工单状态为“待补充信息”。
    *   信息充足时，根据`system_guide.md`构建持久化的待办任务列表。
3.  **浏览器操作**:
    *   使用 Playwright 或 BrowserUse MCP 执行待办任务。
    *   执行前检查操作指引版本，若不匹配则重新构建任务。
    *   执行中如遇信息缺失，则暂停并请求用户补充。
4.  **报告生成**:
    *   任务完成后，生成包含文字总结和操作截图的报告。
    *   更新报告至工单系统，并将工单状态标记为“已完成”。
5.  **手动控制**:
    *   提供全局的 **<开启 | 暂停>** 控制按钮。
    *   暂停后，用户可手动与AI助手交互，完成指定工单。
    *   **截图时机**: 完成一个任务就截图一次。例如涉及多门店操作时，通常一次只能操作一个门店，完成一个门店操作就需要截图保存。

#### 2.2.2. 交互设计

*   **界面**: 采用经典的CUI（命令行用户界面）风格。
*   **输入框**:
    *   支持Markdown和富文本。
    *   支持点击上传、拖拽或粘贴方式上传图片。
    *   可放大以获得更大的编辑区域。
*   **对话界面**:
    *   顶部置顶当前工单号、任务状态和全局控制按钮。
    *   用户可随时打断AI，补充信息，AI需根据新信息动态调整任务。

---

## 3. 技术规格

### 3.1. 技术栈

*   **后端**: Node.js
*   **浏览器自动化**: Playwright 或 BrowserUse MCP (根据项目复杂度和稳定性进行选型)
*   **通信**: WebSocket (用于AI助手和工单系统的实时通信)

### 3.2. 大模型集成

*   **连接方式**: 使用 `openai` 库连接阿里云百炼模型。
*   **模型**:
    *   **主模型**: `qwen-turbo` (用于核心逻辑处理)
    *   **VL模型**: `qwen-vl-plus-2025-01-25` (用于图像理解)
*   **API密钥管理**: 密钥和模型配置必须存储在 `.env` 文件中，严禁硬编码。
    *   `DASHSCOPE_API_KEY`: "sk-7416a845a80443db82afc35951e804e7"
    *   `BASE_URL`: "https://dashscope.aliyuncs.com/compatible-mode/v1"

### 3.3. 图片处理

*   **输入图片**:
    *   发送给VL模型前，等比缩放至最大宽度800px，最大高度600px。
    *   压缩图片质量至80%。
    *   以Base64格式传递给模型。
*   **输出截图**: AI助手需自行实现浏览器窗口或元素的截图功能。

### 3.4. 文件结构

```
project_RPA/
├── src/
│   ├── prompt/
│   │   ├── main_model.md
│   │   └── vl_model.md
│   └── ... (其他业务代码)
├── system_guide.md
├── .env
└── package.json
```

*   **`src/prompt/`**: 存放提示词模板。
*   **`system_guide.md`**: 存放标准操作流程(SOP)。
*   **`.env`**: 存放环境变量和API密钥。

#### 3.4.1. 提示词管理

提示词文件位于 `src/prompt/` 目录，分为主模型和VL模型两个文件：
- `main_model.md`: 主模型提示词
- `vl_model.md`: VL模型提示词

**重要**: 每次请求大模型时，必须从对应的提示词文件中读取最新的提示词内容，严禁硬编码在业务代码中。

#### 3.4.2. 操作指引示例格式

操作指引位于项目根目录的 `system_guide.md` 文档中，包含了意图和操作路径，需严格按照指引执行。示例格式如下：

```markdown
# 管理后台地址
BD商户后台:
- 主地址:https://uat-merchant.aomiapp.com/#/bdlogin
- 备选地址:https://uat-merchant.aomiapp.com/#/select?noDirect=1

## 下架门店外卖商品
1. 打开BD商户后台；
2. 如需登录,请要求用户帮忙登录,登录成功后才继续；
3. 在<门店输入框输>入完整门店 id 或门店名称关键词筛选门店,点击目标门店,并点击<查询>进入商户管理后台;
4. 如点击<查询>没有进入商户管理后台,则点击页面上的相应门店名称,即可进入;
5. 在商户管理后台点击左侧菜单的<系统管理>展开菜单,点击<门店管理>,显示该商户下的门店列表;
6. 在门店列表点击目标门店的<进入门店>按钮,进入门店管理后台(页面顶部会显示门店名称);
7. 在门店管理后台点击左侧菜单的<商品管理>展开菜单,点击<外卖商品管理>,显示外卖商品列表;
8. 在外卖商品列表的搜索框,根据需要,选择搜索类型{商品名称, 商品 id},然后输入关键词,并点击<🔍>搜索;
9. 在搜索列表滚动查找目标商品,找到后点击<下架>,二次确认后即可下架;
10. 找到所有该下架的商品并操作下架后,即完成操作;
```

---
