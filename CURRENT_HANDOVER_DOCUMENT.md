# RPA自动化平台项目交接文档

## 目录
1. [当前工作状态](#1-当前工作状态)
2. [待办事项清单](#2-待办事项清单)
3. [技术依赖关系](#3-技术依赖关系)
4. [重要注意事项](#4-重要注意事项)
5. [快速启动指南](#5-快速启动指南)
6. [MCP架构实现详情](#6-mcp架构实现详情)

## 1. 当前工作状态

### 正在进行的具体任务和进度

**主要任务：修复AI助手面板实时执行步骤显示功能**

**当前进度：90%完成**

#### 已完成的关键步骤：

1. **问题诊断完成** ✅
   - 确认AI助手面板"最近任务"显示为空的根本原因
   - 发现WebSocket事件处理链路中断问题

2. **后端修复完成** ✅
   - 修复了AI助手服务中执行步骤记录功能
   - 确认TaskProcessor正确记录执行步骤到`executionSteps`数组
   - 验证WebSocket推送机制正常工作

3. **前端修复完成** ✅
   - 修复WebSocket store中缺失的`execution_step_update`事件处理
   - 添加`handleExecutionStepUpdate`方法到WebSocket store
   - 修复AI助手面板的WebSocket事件监听机制
   - 将`addEventListener`改为正确的`on`方法调用

4. **代码修改详情**：
   ```javascript
   // src/workorder-system/frontend/src/store/websocket.js
   // 添加了execution_step_update事件处理
   case 'execution_step_update':
     handleExecutionStepUpdate(message.data)
     break
   
   // src/workorder-system/frontend/src/components/AIAssistantPanel.vue  
   // 修复了WebSocket事件监听
   wsStore.on('execution_step_update', handleExecutionStepUpdate)
   ```

#### 遇到的问题和解决方案：

1. **问题**：AI助手面板显示"暂无任务历史"
   - **原因**：WebSocket store没有处理`execution_step_update`事件
   - **解决方案**：在WebSocket store的消息处理switch语句中添加对应的case分支

2. **问题**：前端WebSocket事件监听方法错误
   - **原因**：使用了不存在的`addEventListener`方法
   - **解决方案**：改为使用WebSocket store提供的`on`方法

3. **问题**：前端服务启动失败
   - **原因**：服务进程管理问题
   - **当前状态**：需要重新启动前端服务进行最终验证

### 当前系统运行状态：

- ✅ **工单系统后端**：正常运行 (端口3001)
- ✅ **AI助手服务**：正常运行 (端口3002)  
- ✅ **Playwright MCP服务**：正常运行 (端口8931)
- ❌ **工单系统前端**：需要重启 (端口3003)

## 2. 待办事项清单

### 高优先级任务

#### 任务1：完成前端服务重启和功能验证 🔴
- **具体要求**：
  - 重新启动工单系统前端服务
  - 验证AI助手面板能正确显示实时执行步骤
  - 确认WebSocket连接和事件推送正常工作
- **验收标准**：
  - AI助手面板"最近任务"区域显示具体的执行步骤
  - 步骤描述为中文且具体（如"点击商品管理菜单"而非"点击元素"）
  - 实时更新无延迟
- **预估时间**：30分钟

#### 任务2：创建测试工单验证完整流程 🔴
- **具体要求**：
  - 创建一个新的测试工单
  - 观察AI助手执行过程中的步骤记录
  - 验证前端实时显示功能
- **验收标准**：
  - 工单状态正确更新
  - AI助手面板实时显示执行步骤
  - 步骤描述准确且易理解
- **预估时间**：20分钟

### 中优先级任务

#### 任务3：优化执行步骤描述质量 🟡
- **具体要求**：
  - 检查`generateStepDescription`方法的实现
  - 确保生成的步骤描述更加友好和具体
  - 添加更多操作类型的中文描述
- **验收标准**：
  - 所有操作类型都有对应的中文描述
  - 描述包含具体的操作目标信息
- **预估时间**：1小时

#### 任务4：添加错误处理和重连机制 🟡
- **具体要求**：
  - 完善WebSocket断线重连逻辑
  - 添加执行步骤推送失败的容错处理
  - 优化前端错误提示信息
- **验收标准**：
  - WebSocket断线后能自动重连
  - 推送失败不影响系统稳定性
  - 用户能看到清晰的错误提示
- **预估时间**：2小时

### 低优先级任务

#### 任务5：性能优化 🟢
- **具体要求**：
  - 优化执行步骤存储的内存使用
  - 实现步骤历史的分页加载
  - 添加步骤过滤和搜索功能
- **预估时间**：4小时

## 3. 技术依赖关系

### 系统架构图
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   前端 Vue3     │    │   工单系统后端   │    │   AI助手服务    │
│   (端口3003)    │◄──►│   (端口3001)     │◄──►│   (端口3002)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌────────▼────────┐             │
         │              │  WebSocket服务   │             │
         │              │  (端口3001/ws)   │             │
         │              └─────────────────┘             │
         │                                              │
         └──────────────────────────────────────────────┼──────┐
                                                        │      │
                                              ┌─────────▼──────▼─┐
                                              │ Playwright MCP   │
                                              │   (端口8931)     │
                                              └──────────────────┘
```

### 服务启动顺序
1. **Playwright MCP服务** (必须最先启动)
   ```bash
   npx @playwright/mcp --port 8931 --isolated
   ```

2. **工单系统后端** 
   ```bash
   npm run dev:workorder-backend
   ```

3. **AI助手服务**
   ```bash
   cd src/ai-assistant && npm start
   ```

4. **工单系统前端**
   ```bash
   npm run dev:workorder-frontend
   ```

### 端口配置
- **3001**: 工单系统后端API + WebSocket服务
- **3002**: AI助手服务API
- **3003**: 工单系统前端
- **8931**: Playwright MCP服务

### 关键配置文件

#### WebSocket配置
- **后端**: `src/workorder-system/backend/src/websocket/websocket-service.js`
- **前端**: `src/workorder-system/frontend/src/store/websocket.js`

#### AI助手配置  
- **主服务**: `src/ai-assistant/server.js`
- **任务处理器**: `src/ai-assistant/src/task-processor.js`
- **智能执行器**: `src/ai-assistant/src/core/intelligent-executor.js`

#### 环境变量
```bash
# AI助手服务需要的环境变量
QWEN_API_KEY=your_qwen_api_key
QWEN_MODEL=qwen-turbo  # 推荐使用qwen-turbo，无频率限制
```

## 4. 重要注意事项

### 已知技术限制和约束

1. **AI模型限制**
   - qwen-turbo-2025-04-28有429频率限制
   - 推荐使用qwen-turbo模型（无限制）
   - 模型配置在.env文件中，不要硬编码

2. **WebSocket连接稳定性**
   - 连接可能因网络问题断开
   - 需要实现自动重连机制
   - 前端需要处理连接状态变化

3. **执行步骤存储**
   - 内存中最多保存50条步骤记录
   - 超出限制会自动清理旧记录
   - 没有持久化存储

### 调试和测试要点

1. **WebSocket调试**
   ```javascript
   // 在浏览器控制台检查WebSocket连接
   console.log('WebSocket状态:', wsStore.connected)
   console.log('最后收到的消息:', wsStore.lastMessage)
   ```

2. **AI助手服务调试**
   ```bash
   # 检查最近任务
   curl -s http://localhost:3002/recent-tasks?limit=10 | jq .
   
   # 检查执行步骤
   curl -s http://localhost:3002/execution-steps?limit=10 | jq .
   ```

3. **关键日志位置**
   - AI助手服务日志: `src/ai-assistant/ai-assistant.log`
   - 浏览器控制台: WebSocket事件和前端错误
   - 终端输出: 服务启动和运行状态

### 可能的风险点和规避方法

1. **风险**: WebSocket连接频繁断开
   - **规避**: 实现指数退避重连策略
   - **监控**: 检查reconnectAttempts计数

2. **风险**: AI助手服务内存泄漏
   - **规避**: 定期清理执行步骤历史
   - **监控**: 观察内存使用情况

3. **风险**: 前端页面刷新丢失实时数据
   - **规避**: 页面加载时主动获取最近步骤
   - **备选**: 实现本地存储缓存

## 5. 快速启动指南

### 环境搭建步骤

1. **安装依赖**
   ```bash
   # 根目录
   npm install
   
   # AI助手服务
   cd src/ai-assistant && npm install
   
   # 工单系统前端
   cd src/workorder-system/frontend && npm install
   ```

2. **配置环境变量**
   ```bash
   # 在src/ai-assistant目录创建.env文件
   echo "QWEN_API_KEY=your_api_key" > src/ai-assistant/.env
   echo "QWEN_MODEL=qwen-turbo" >> src/ai-assistant/.env
   ```

### 服务启动命令

```bash
# 终端1: 启动Playwright MCP (必须最先启动)
npx @playwright/mcp --port 8931 --isolated

# 终端2: 启动工单系统后端
npm run dev:workorder-backend

# 终端3: 启动AI助手服务  
cd src/ai-assistant && npm start

# 终端4: 启动工单系统前端
npm run dev:workorder-frontend
```

### 验证系统正常运行

1. **检查服务状态**
   ```bash
   # 检查端口占用
   lsof -i :3001,3002,3003,8931
   
   # 检查API响应
   curl http://localhost:3001/api/health
   curl http://localhost:3002/status
   ```

2. **验证前端功能**
   - 访问 http://localhost:3003
   - 检查AI助手面板状态显示
   - 创建测试工单观察实时更新

3. **验证WebSocket连接**
   - 打开浏览器开发者工具
   - 查看Network标签页的WebSocket连接
   - 确认消息正常收发

### 常见问题快速解决

1. **前端无法连接后端**
   ```bash
   # 检查后端服务是否启动
   curl http://localhost:3001/api/health
   
   # 重启后端服务
   npm run dev:workorder-backend
   ```

2. **AI助手显示离线**
   ```bash
   # 检查AI助手服务
   curl http://localhost:3002/status
   
   # 重启AI助手服务
   cd src/ai-assistant && npm start
   ```

3. **WebSocket连接失败**
   - 检查浏览器控制台错误信息
   - 确认WebSocket服务端口3001可访问
   - 尝试刷新页面重新连接

## 6. MCP架构实现详情

### 6.1 MCP架构概述

**Model Context Protocol (MCP)** 是本项目的核心架构，实现了AI模型与浏览器自动化的无缝集成。

#### 核心优势
- **真正的AI-First架构**：AI模型直接控制浏览器操作
- **标准化协议**：使用JSON-RPC 2.0进行通信
- **高可靠性**：MCP服务独立运行，故障隔离
- **实时反馈**：每个操作都有即时的页面状态反馈

### 6.2 MCP通信流程

```mermaid
sequenceDiagram
    participant AI as AI助手服务
    participant MCP as Playwright MCP
    participant Browser as 浏览器实例

    AI->>MCP: JSON-RPC请求 (browser_navigate)
    MCP->>Browser: 执行Playwright操作
    Browser-->>MCP: 页面状态变化
    MCP->>AI: 返回操作结果+页面快照
    AI->>AI: 分析结果，规划下一步
    AI->>MCP: 下一个操作请求
```

### 6.3 关键实现文件

#### MCP客户端实现
- **文件**: `src/ai-assistant/src/core/intelligent-executor.js`
- **功能**:
  - 建立与MCP服务的SSE连接
  - 发送JSON-RPC 2.0格式的工具调用请求
  - 处理MCP服务返回的页面状态和操作结果

#### 核心代码片段
```javascript
// MCP工具调用实现
async callMCPTool(toolName, args) {
    const request = {
        jsonrpc: "2.0",
        id: this.requestId++,
        method: "tools/call",
        params: {
            name: toolName,
            arguments: args
        }
    };

    // 通过SSE连接发送请求
    const response = await fetch(this.postEndpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request)
    });

    return this.waitForSSEResponse(request.id);
}
```

### 6.4 支持的MCP工具

#### 浏览器导航工具
- `browser_navigate`: 导航到指定URL
- `browser_snapshot`: 获取页面可访问性快照
- `browser_click`: 点击页面元素
- `browser_type`: 在输入框中输入文本
- `browser_select_option`: 选择下拉框选项

#### 工具调用示例
```javascript
// 导航到页面
await this.callMCPTool('browser_navigate', {
    url: 'https://uat-merchant.aomiapp.com/#/bdlogin'
});

// 点击元素
await this.callMCPTool('browser_click', {
    element: '商品管理菜单',
    ref: 'e1406'
});

// 输入文本
await this.callMCPTool('browser_type', {
    element: '门店名称输入框',
    ref: 'e1234',
    text: '小泉居威翠店'
});
```

### 6.5 页面状态分析

#### 可访问性快照格式
MCP服务返回的页面快照采用YAML格式，包含：
- 页面元素的层次结构
- 每个元素的引用ID (ref)
- 元素的可交互状态 (cursor=pointer)
- 文本内容和标签信息

#### 快照示例
```yaml
- generic [ref=e1406]:
  - generic [ref=e1407] [cursor=pointer]:
    - img [ref=e1409] [cursor=pointer]
    - generic [ref=e1410] [cursor=pointer]: 商品管理
    - generic [ref=e1411] [cursor=pointer]:
```

### 6.6 AI决策机制

#### 智能规划流程
1. **页面分析**: AI分析当前页面快照
2. **目标识别**: 根据任务要求识别下一步操作目标
3. **工具选择**: 选择合适的MCP工具
4. **参数生成**: 生成工具调用参数
5. **结果验证**: 分析操作结果，调整后续策略

#### 动态适应能力
- **错误恢复**: 操作失败时自动尝试替代方案
- **页面变化适应**: 根据页面结构变化调整操作策略
- **智能等待**: 自动处理页面加载和异步更新

### 6.7 执行步骤记录

#### 步骤记录机制
每个MCP工具调用都会被记录为执行步骤：

```javascript
// 记录执行步骤
this.taskProcessor.addExecutionStep({
    id: `step-${Date.now()}`,
    title: stepInfo.title,
    description: stepInfo.description,
    status: 'completed',
    timestamp: new Date().toISOString(),
    tool: operationType,
    element: elementRef,
    pageUrl: pageUrl
});
```

#### 步骤描述生成
```javascript
generateStepDescription(operationType, toolArguments, elementRef, pageUrl) {
    const descriptions = {
        'browser_navigate': {
            title: '导航到页面',
            description: `导航到${toolArguments.url || '目标页面'}`
        },
        'browser_click': {
            title: `点击${toolArguments.element || '元素'}`,
            description: `点击页面元素: ${toolArguments.element || elementRef}`
        },
        'browser_type': {
            title: `输入文本到${toolArguments.element || '输入框'}`,
            description: `在${toolArguments.element || '输入框'}中输入: ${toolArguments.text}`
        }
    };

    return descriptions[operationType] || {
        title: '执行操作',
        description: `执行${operationType}操作`
    };
}
```

### 6.8 MCP服务配置

#### 启动参数
```bash
npx @playwright/mcp --port 8931 --isolated
```

#### 关键配置
- **端口**: 8931 (固定端口，确保服务发现)
- **隔离模式**: `--isolated` 确保浏览器实例独立
- **协议**: JSON-RPC 2.0 over HTTP + SSE

### 6.9 故障排除

#### 常见MCP问题

1. **MCP服务连接失败**
   ```bash
   # 检查MCP服务状态
   curl http://localhost:8931/health

   # 重启MCP服务
   npx @playwright/mcp --port 8931 --isolated
   ```

2. **工具调用超时**
   - 检查页面加载状态
   - 增加操作等待时间
   - 验证元素引用ID的有效性

3. **页面快照解析错误**
   - 确认页面完全加载
   - 检查页面结构变化
   - 验证元素可访问性

#### 调试技巧
```javascript
// 启用MCP调试日志
logger.debug('MCP请求:', request);
logger.debug('MCP响应:', response);

// 保存页面快照用于分析
fs.writeFileSync('debug_snapshot.yaml', pageSnapshot);
```

---

## 下一步行动建议

1. **立即执行**: 重启前端服务并验证修复效果
2. **短期目标**: 完成功能验证和测试用例
3. **中期目标**: 优化用户体验和错误处理
4. **长期目标**: 性能优化和功能扩展

**预计完整修复时间**: 1-2小时
**关键验收标准**: AI助手面板能实时显示中文执行步骤描述

---

## 技术架构总结

本项目采用了先进的**AI-First RPA架构**，核心特点：

1. **MCP协议驱动**: 使用标准化的Model Context Protocol实现AI与浏览器的直接通信
2. **实时状态反馈**: 每个操作都有即时的页面状态反馈，确保AI决策的准确性
3. **智能自适应**: AI能够根据页面变化动态调整操作策略
4. **完整可观测性**: 从工具调用到执行步骤的完整记录和实时推送

这种架构确保了RPA系统的**高可靠性**、**强适应性**和**完全可观测性**，是现代AI驱动自动化的最佳实践。
