/**
 * 创建测试工单
 */

const axios = require('axios');

async function createTestWorkOrder() {
    console.log('🎯 创建测试工单...');

    const testWorkOrder = {
        title: '下架小泉居(威翠店)的咖哩焗魚飯',
        content: '下架小泉居(威翠店)的咖哩焗魚飯商品',
        priority: 'high',
        type: 'product_management',
        requester: 'BD运营',
        department: '运营部',
        expectedDuration: 600, // 10分钟
        tags: ['商品下架', '小泉居', '威翠店', 'RPA']
    };

    try {
        const response = await axios.post('http://localhost:3001/api/tickets', testWorkOrder);
        
        if (response.status === 201) {
            console.log('✅ 测试工单创建成功！');
            console.log('📋 工单信息:');
            console.log(`   ID: ${response.data.id}`);
            console.log(`   标题: ${response.data.title}`);
            console.log(`   状态: ${response.data.status}`);
            console.log(`   创建时间: ${response.data.createdAt}`);
            console.log('\n🔍 请观察AI助手日志，查看工单处理过程...');
            console.log('📱 也可以在前端界面查看工单状态: http://localhost:3000');
            
            return response.data;
        } else {
            console.log('❌ 工单创建失败:', response.status, response.statusText);
        }
    } catch (error) {
        console.log('❌ 工单创建异常:', error.message);
        if (error.response) {
            console.log('   响应状态:', error.response.status);
            console.log('   响应数据:', error.response.data);
        }
    }
}

// 运行测试
if (require.main === module) {
    createTestWorkOrder().catch(console.error);
}

module.exports = { createTestWorkOrder };
