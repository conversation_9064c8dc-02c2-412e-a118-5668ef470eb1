# AI助手系统测试指南

## 📋 测试概述

本测试套件用于验证AI助手系统的核心功能，特别是基于`system_guide.md`的工单解析和任务生成能力。

### 🎯 测试目标

1. **AI模型连接测试** - 验证与阿里云百炼模型的连接
2. **工单内容解析** - 验证AI助手理解工单需求的能力
3. **任务生成验证** - 验证生成的任务是否符合system_guide.md要求
4. **System Guide合规性** - 验证操作流程是否严格遵循标准
5. **真实场景测试** - 使用实际门店数据进行测试

### 🏪 测试门店信息

- **门店名称**: CAFÉ E.S.KIMO 小泉居(威翠店)
- **门店ID**: 12268
- **完整标识**: CAFÉ E.S.KIMO 小泉居(威翠店)（12268）

## 🚀 快速开始

### 1. 环境准备

```bash
# 1. 确保已安装依赖
npm run install:ai

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，设置 DASHSCOPE_API_KEY
```

### 2. 运行快速测试

```bash
# 快速测试（推荐）
npm run test:ai-quick

# 或直接运行
node test-ai-assistant.js
```

### 3. 运行完整测试

```bash
# 完整测试套件
npm run test:ai-full

# 或直接运行
cd src/ai-assistant && node run-tests.js
```

## 📊 测试内容详解

### 测试1: AI模型连接测试
- **目的**: 验证API密钥和模型服务可用性
- **预期**: 成功连接并返回测试响应

### 测试2: 基础工单解析测试
- **输入**: 简单的门店商品下架请求
- **预期**: 生成包含关键步骤的任务列表

### 测试3: 门店商品下架场景测试
- **输入**: 详细的下架请求（包含门店ID 12268）
- **预期**: 生成符合system_guide.md流程的完整任务

### 测试4: 信息不足处理测试
- **输入**: 信息不完整的工单
- **预期**: 返回`need_info`状态并列出需要补充的信息

### 测试5: 多模态解析测试
- **输入**: 包含图片的工单
- **预期**: 使用VL模型处理图片内容

### 测试6: 错误处理测试
- **输入**: 空内容或格式错误的工单
- **预期**: 正确处理错误并返回相应状态

### 测试7: System Guide合规性测试
- **验证项目**:
  - ✅ 使用正确的BD商户后台地址
  - ✅ 包含登录验证步骤
  - ✅ 包含门店搜索步骤（使用ID 12268）
  - ✅ 包含下架操作步骤
  - ✅ 包含截图确认步骤

### 测试8: 真实场景测试
- **数据源**: `src/ai-assistant/tests/test-tickets.json`
- **场景**: 多种真实工单场景的批量测试

## 📋 System Guide合规性检查

基于`system_guide.md`的标准流程，AI助手生成的任务必须包含以下步骤：

1. **打开BD商户后台** (`https://uat-merchant.aomiapp.com/#/bdlogin`)
2. **登录验证** (要求用户手动登录)
3. **门店搜索** (输入门店ID或名称)
4. **点击查询** 进入商户管理后台
5. **系统管理 → 门店管理**
6. **点击进入门店**
7. **商品管理 → 外卖商品管理**
8. **商品搜索** (按名称或ID)
9. **点击下架** 
10. **二次确认**
11. **截图保存**

## 🔧 测试配置

### 环境变量

```bash
# 必需配置
DASHSCOPE_API_KEY=sk-your-api-key-here

# 可选配置
BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
MAIN_MODEL=qwen-turbo
VL_MODEL=qwen-vl-plus-2025-01-25
LOG_LEVEL=info
```

### 测试数据

测试使用的工单示例：

```json
{
  "id": "TEST_001",
  "title": "下架CAFÉ E.S.KIMO小泉居威翠店招牌咖啡",
  "content": "请帮我下架门店\"CAFÉ E.S.KIMO 小泉居(威翠店)（12268）\"的外卖商品\"招牌咖啡\""
}
```

## 📈 测试结果解读

### 成功指标

- ✅ **AI模型连接**: 成功连接并响应
- ✅ **工单解析**: 正确理解工单需求
- ✅ **任务生成**: 生成完整的操作步骤
- ✅ **合规性检查**: 通过所有System Guide验证
- ✅ **关键步骤**: 包含导航、登录、搜索、下架、截图等步骤

### 常见问题

1. **API连接失败**
   - 检查DASHSCOPE_API_KEY是否正确
   - 确认网络连接正常
   - 验证API配额是否充足

2. **解析结果不符合预期**
   - 检查提示词文件是否存在
   - 验证system_guide.md内容是否正确
   - 确认模型版本配置

3. **合规性检查失败**
   - 检查生成的任务是否包含必需步骤
   - 验证URL和选择器是否正确
   - 确认操作流程符合标准

## 🛠️ 调试和优化

### 启用详细日志

```bash
LOG_LEVEL=debug node test-ai-assistant.js
```

### 查看生成的任务详情

测试会显示生成任务的前5个步骤，完整任务可在测试输出中查看。

### 自定义测试

可以修改`src/ai-assistant/tests/test-tickets.json`添加自定义测试场景。

## 📞 支持

如果测试过程中遇到问题：

1. 检查环境配置是否正确
2. 查看控制台错误信息
3. 验证API密钥和网络连接
4. 参考测试输出的详细信息

---

**测试门店**: CAFÉ E.S.KIMO 小泉居(威翠店)（12268）  
**最后更新**: 2025-07-08
