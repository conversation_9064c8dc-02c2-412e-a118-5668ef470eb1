
# RPA 自动化平台 - 技术架构文档

## 1. 概述

本文档旨在根据项目需求（PRD）定义 RPA 自动化平台的技术架构。该平台的核心是利用 AI 驱动的自动化流程，替代人工操作，实现业务流程的无人化管理。

## 2. 架构设计原则

- **模块化**: 系统功能应高度解耦，便于独立开发、测试和维护。
- **可扩展性**: 架构应支持未来新功能、新流程和更多第三方系统的集成。
- **稳定性与可靠性**: 保证长时间稳定运行，并具备错误处理和恢复机制。
- **安全性**: 保护敏感数据（如 API 密钥、用户凭据）和系统访问安全。

## 3. 系统架构图

```mermaid
graph TD
    subgraph 用户端
        A[浏览器]
    end

    subgraph 工单系统 (Node.js + Express + Vue/React)
        B[前端界面] -- HTTP/HTTPS --> C[后端 API]
        C -- WebSocket --> D[AI 助手]
        C -- CRUD --> E[数据库 (PostgreSQL/MongoDB)]
    end

    subgraph AI 助手 (Node.js)
        D -- 获取工单 --> C
        D -- 更新工单 --> C
        D -- 任务拆解/决策 --> F[主模型 (qwen-turbo)]
        D -- 图片理解 --> G[VL 模型 (qwen-vl-plus)]
        D -- 浏览器操作 --> H[Playwright]
        D -- 读取 --> I[system_guide.md]
        D -- 读取 --> J[prompt/]
        D -- 配置管理 --> L[模型配置管理]
        D -- 版本检查 --> M[操作指引版本管理]
        D -- 用户交互 --> N[CUI 界面]
    end

    subgraph 目标系统
        K[现有管理后台]
    end

    A --> B
    H -- 操作 --> K
```

## 4. 模块详解

### 4.1. 工单系统

#### 4.1.1. 前端

- **技术栈**: 推荐使用 Vue.js 或 React.js 等现代前端框架，配合 Element Plus 或 Ant Design 等组件库，快速构建高质量的管理界面。
- **核心功能**:
    - 工单的增删改查界面。
    - 实时状态更新（通过 WebSocket 接收来自 AI 助手的通知）。
    - 富文本编辑器（集成 Markdown-It 或其他类似库）。
    - 文件上传（用于在工单中附加图片）。

#### 4.1.2. 后端

- **技术栈**: Node.js + Express.js 或 NestJS。
- **API**: 提供 RESTful API 供前端调用，用于工单管理、用户认证等。
- **数据库**: 推荐使用 SQLite（轻量级，适合演示）。
    - **数据表/集合**:
        - `tickets`: 存储工单信息（工单号、内容、状态、备注、总结等）。
        - `tasks`: 存储每个工单关联的待办任务列表及状态。
        - `users`: 用户信息及权限管理。
- **WebSocket 服务**: 建立与 AI 助手的长连接，用于实时推送工单更新和接收状态变更。

### 4.2. AI 助手

AI 助手是整个系统的“大脑”，负责执行自动化任务。它是一个独立的 Node.js 应用。

#### 4.2.1. 核心流程控制器 (Orchestrator)

- **工单队列**: 从工单系统获取“待开始”的工单，并按顺序放入执行队列。
- **状态机**: 管理工单的生命周期（待开始 -> 处理中 -> ... -> 已完成/已挂起）。
- **任务调度**: 调用任务构建模块和浏览器操作模块，驱动整个自动化流程。

#### 4.2.2. 任务构建模块 (Task Builder)

- **输入处理**: 接收工单内容，如果是多模态（含图片），先调用 VL 模型处理图片。
- **意图识别**: 将工单内容和 `system_guide.md`、`prompt/main_model.md` 中的提示词结合，发送给主模型，识别用户意图。
- **任务拆解**: 如果意图明确，主模型会返回一个结构化的待办任务列表（JSON 格式），该列表将被持久化到数据库。
- **信息补全**: 如果主模型认为信息不足，则更新工单状态为“待补充信息”，并附上需要补充的问题。

#### 4.2.4. 模型配置管理模块 (Model Configuration Manager)

- **配置加载**: 从 `.env` 文件加载模型配置信息：
  - `DASHSCOPE_API_KEY`: "sk-7416a845a80443db82afc35951e804e7"
  - `BASE_URL`: "https://dashscope.aliyuncs.com/compatible-mode/v1"
  - `MAIN_MODEL`: "qwen-turbo"
  - `VL_MODEL`: "qwen-vl-plus-2025-01-25"
- **提示词管理**: 每次请求大模型时，动态从以下文件读取提示词：
  - `src/prompt/main_model.md`: 主模型提示词
  - `src/prompt/vl_model.md`: VL模型提示词
- **连接管理**: 维护与阿里云百炼模型的连接，处理请求重试和错误处理

#### 4.2.5. 操作指引版本管理模块 (Guide Version Manager)

- **版本检查**: 在执行任务前，检查当前待办任务使用的操作指引版本是否为最新版本
- **版本更新**: 如果版本不匹配，重新读取 `system_guide.md` 并重新构建任务
- **版本标记**: 为每个待办任务添加操作指引版本标识

#### 4.2.6. CUI 界面模块 (Conversational UI)

- **界面设计**: 采用经典的命令行用户界面风格，类似 Claude 等 AI 助手
- **核心组件**:
  - 顶部状态栏：显示当前工单号、任务状态、全局控制按钮（开启/暂停）
  - 对话区域：显示与AI助手的交互历史
  - 输入区域：支持富文本、Markdown、图片上传（点击、拖拽、粘贴）
  - 可展开编辑区：点击可放大输入框，获得更大编辑空间
- **交互特性**:
  - 用户可随时打断AI任务执行
  - 支持实时补充信息和调整任务
  - 显示任务执行进度和截图结果

- **技术选型**: **Playwright**。它提供了强大的自动化能力、可靠的等待机制和跨浏览器支持，非常适合本项目。
- **执行引擎**: 遍历待办任务列表，根据每个任务的指令（如“点击按钮”、“输入文本”）调用 Playwright 的 API 执行操作。
- **截图与报告**: 每完成一个关键步骤，调用 Playwright 的截图功能，并将截图（Base64）和文字小结存入数据库，用于最终的完单报告。
- **错误处理**: 捕获 Playwright 执行过程中的异常（如元素未找到、页面加载失败），并根据情况重试或上报错误。

### 4.3. 通信机制

#### 4.3.1. 工单状态流转实现

工单状态流转遵循以下状态机逻辑：

```mermaid
stateDiagram-v2
    [*] --> 待开始
    待开始 --> 已挂起: 用户操作
    待开始 --> 处理中: AI助手开始处理
    处理中 --> 已挂起: 用户操作(需二次确认)
    处理中 --> 待补充信息: AI助手发现信息不足
    处理中 --> 已完成: AI助手完成所有任务
    待补充信息 --> 已挂起: 用户操作
    待补充信息 --> 待开始: 用户补充信息后
    已挂起 --> 待开始: 用户操作
    已完成 --> [*]: 工单生命周期结束
```

**状态流转规则**:
- 只有"待开始"、"已挂起"、"已完成"状态的工单可以删除
- "处理中"状态的工单用户只能查看和挂起（需二次确认）
- "待补充信息"状态仅由AI助手设置，用户补充信息后自动转为"待开始"
- AI助手按队列顺序处理"待开始"状态的工单

#### 4.3.2. 系统间通信

- **工单系统与 AI 助手**: 使用 WebSocket 进行双向实时通信。
    - **工单系统 -> AI 助手**: 推送新工单、用户操作（如“挂起”）。
    - **AI 助手 -> 工单系统**: 实时更新工单状态、任务进度、完单报告等。
- **AI 助手与大模型**: 通过 HTTPS 调用阿里云百炼的 API。

## 5. 数据管理

- **环境变量**: 所有敏感信息（API Key, 数据库连接字符串等）必须通过 `.env` 文件管理。
- **提示词**: 提示词模板存储在 `src/prompt/` 目录下的 Markdown 文件中，在运行时动态加载。
- **操作指引**: `system_guide.md` 作为核心知识库，被 AI 助手用于生成和校验任务。

## 6. 本机演示部署

### 6.1. 环境要求

- **操作系统**: macOS (M1 Pro 支持)
- **Node.js**: v18+ (推荐 v20 LTS)
- **数据库**: SQLite (轻量级，适合演示)
- **浏览器**: Chrome/Chromium (Playwright 依赖)

### 6.2. 项目结构

```
project_RPA/
├── workorder-system/          # 工单系统
│   ├── frontend/             # 前端 (Vue.js)
│   └── backend/              # 后端 (Node.js + Express)
├── ai-assistant/             # AI助手
│   ├── src/
│   │   ├── prompt/          # 提示词文件
│   │   ├── config/          # 配置管理
│   │   ├── models/          # 模型调用
│   │   ├── browser/         # 浏览器操作
│   │   └── ui/              # CUI界面
│   └── data/                # SQLite数据库
├── system_guide.md          # 操作指引
├── .env                     # 环境变量
└── package.json
```

### 6.3. 快速启动

```bash
# 安装依赖
npm install

# 启动工单系统
npm run start:workorder

# 启动AI助手
npm run start:assistant
```

### 6.4. 数据存储

- **SQLite**: 轻量级数据库，数据文件存储在 `ai-assistant/data/` 目录
- **文件存储**: 截图和日志存储在本地文件系统
- **无需额外配置**: 适合演示和开发环境
