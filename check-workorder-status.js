/**
 * 检查工单状态
 */

const axios = require('axios');

async function checkWorkOrderStatus() {
    console.log('🔍 检查最新工单状态...');

    try {
        // 获取所有工单
        const response = await axios.get('http://localhost:3001/api/tickets');
        
        if (response.status === 200) {
            const responseData = response.data;
            const tickets = responseData.success ? responseData.data : responseData;
            console.log(`📋 共找到 ${tickets ? tickets.length : 0} 个工单`);
            
            if (tickets && tickets.length > 0) {
                // 按创建时间排序，获取最新的工单
                const latestTicket = tickets.sort((a, b) => 
                    new Date(b.createdAt) - new Date(a.createdAt)
                )[0];
                
                console.log('\n📄 最新工单详情:');
                console.log('='.repeat(50));
                console.log(`ID: ${latestTicket.id}`);
                console.log(`标题: ${latestTicket.title}`);
                console.log(`状态: ${latestTicket.status}`);
                console.log(`优先级: ${latestTicket.priority}`);
                console.log(`类型: ${latestTicket.type}`);
                console.log(`申请人: ${latestTicket.requester}`);
                console.log(`部门: ${latestTicket.department}`);
                console.log(`创建时间: ${latestTicket.createdAt}`);
                console.log(`更新时间: ${latestTicket.updatedAt}`);
                
                if (latestTicket.assignedTo) {
                    console.log(`分配给: ${latestTicket.assignedTo}`);
                }
                
                if (latestTicket.completedAt) {
                    console.log(`完成时间: ${latestTicket.completedAt}`);
                }
                
                if (latestTicket.result) {
                    console.log(`\n📊 处理结果:`);
                    console.log(JSON.stringify(latestTicket.result, null, 2));
                }
                
                console.log(`\n📝 工单内容:`);
                console.log(latestTicket.content);
                
                // 状态解释
                console.log('\n📈 状态说明:');
                switch (latestTicket.status) {
                    case '待开始':
                        console.log('⏳ 工单已创建，等待AI助手处理');
                        break;
                    case '排队中':
                        console.log('🔄 工单已进入处理队列');
                        break;
                    case '处理中':
                        console.log('⚡ AI助手正在处理工单');
                        break;
                    case '已完成':
                        console.log('✅ 工单处理完成');
                        break;
                    case '待补充信息':
                        console.log('❓ 需要补充更多信息');
                        break;
                    case '处理失败':
                        console.log('❌ 工单处理失败');
                        break;
                    default:
                        console.log(`❓ 未知状态: ${latestTicket.status}`);
                }
                
                return latestTicket;
            } else {
                console.log('📭 暂无工单');
            }
        } else {
            console.log('❌ 获取工单失败:', response.status, response.statusText);
        }
    } catch (error) {
        console.log('❌ 检查工单状态异常:', error.message);
        if (error.response) {
            console.log('   响应状态:', error.response.status);
            console.log('   响应数据:', error.response.data);
        }
    }
}

// 运行检查
if (require.main === module) {
    checkWorkOrderStatus().catch(console.error);
}

module.exports = { checkWorkOrderStatus };
