const axios = require('axios');
require('dotenv').config({ path: 'src/ai-assistant/.env' });

async function testQwenAPI() {
    const baseUrl = 'https://dashscope.aliyuncs.com/compatible-mode/v1';
    const apiKey = process.env.DASHSCOPE_API_KEY;
    
    console.log('🧪 测试qwen API配置...');
    console.log('API Key:', apiKey ? `${apiKey.substring(0, 10)}...` : '未配置');
    
    // 测试qwen-plus
    console.log('\n📋 测试qwen-plus模型...');
    try {
        const response = await axios.post(`${baseUrl}/chat/completions`, {
            model: 'qwen-plus',
            messages: [
                {
                    role: 'user',
                    content: '你好，请简单回复一下'
                }
            ],
            temperature: 0.1,
            max_tokens: 100
        }, {
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json'
            }
        });
        
        console.log('✅ qwen-plus测试成功');
        console.log('响应:', response.data.choices[0].message.content);
    } catch (error) {
        console.log('❌ qwen-plus测试失败');
        console.log('状态码:', error.response?.status);
        console.log('错误信息:', error.response?.data?.error?.message || error.message);
    }
    

}

testQwenAPI().catch(console.error);
