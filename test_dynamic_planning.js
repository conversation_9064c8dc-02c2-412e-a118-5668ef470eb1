/**
 * 测试动态规划提示词工程效果
 */

const { AITaskAnalyzer } = require('./src/ai-assistant/src/core/ai-task-analyzer');
const { DynamicPromptGenerator } = require('./src/ai-assistant/src/core/dynamic-prompt-generator');

async function testDynamicPlanning() {
    console.log('🧠 开始测试动态规划提示词工程...\n');

    // 创建测试实例
    const taskAnalyzer = new AITaskAnalyzer();
    const promptGenerator = new DynamicPromptGenerator();

    // 测试工单内容
    const testTicket = {
        id: 'TEST_001',
        title: '测试动态规划能力',
        content: '下架小泉居威翠店的咖哩焗魚飯',
        priority: 'high'
    };

    try {
        console.log('📋 测试工单内容:');
        console.log(`- ID: ${testTicket.id}`);
        console.log(`- 标题: ${testTicket.title}`);
        console.log(`- 内容: ${testTicket.content}`);
        console.log(`- 优先级: ${testTicket.priority}\n`);

        // 第1步：AI任务分析
        console.log('🔍 第1步：AI任务分析...');
        const analysis = await taskAnalyzer.analyzeTask(testTicket.content, {
            ticketId: testTicket.id,
            title: testTicket.title,
            priority: testTicket.priority
        });

        console.log('📊 任务分析结果:');
        console.log(JSON.stringify(analysis, null, 2));
        console.log('\n');

        // 第2步：动态提示词生成
        console.log('🎯 第2步：动态提示词生成...');
        const promptConfig = promptGenerator.generatePrompt(analysis, testTicket);

        console.log('📝 生成的系统提示词:');
        console.log('='.repeat(80));
        console.log(promptConfig.systemPrompt);
        console.log('='.repeat(80));
        console.log('\n');

        console.log('📝 生成的任务提示词:');
        console.log('='.repeat(80));
        console.log(promptConfig.taskPrompt);
        console.log('='.repeat(80));
        console.log('\n');

        // 第3步：验证动态规划要素
        console.log('✅ 第3步：验证动态规划要素...');
        
        const hasGoalDecomposition = promptConfig.systemPrompt.includes('目标分解');
        const hasStateAwareness = promptConfig.systemPrompt.includes('状态感知');
        const hasPathOptimization = promptConfig.systemPrompt.includes('路径优化');
        const hasAdaptiveAdjustment = promptConfig.systemPrompt.includes('自适应调整');
        const hasDecisionTemplate = promptConfig.systemPrompt.includes('决策模板');
        const hasStrategicPlan = analysis.strategicPlan && Object.keys(analysis.strategicPlan).length > 0;

        console.log('🧠 动态规划要素检查:');
        console.log(`- 目标分解能力: ${hasGoalDecomposition ? '✅' : '❌'}`);
        console.log(`- 状态感知能力: ${hasStateAwareness ? '✅' : '❌'}`);
        console.log(`- 路径优化能力: ${hasPathOptimization ? '✅' : '❌'}`);
        console.log(`- 自适应调整能力: ${hasAdaptiveAdjustment ? '✅' : '❌'}`);
        console.log(`- 决策模板: ${hasDecisionTemplate ? '✅' : '❌'}`);
        console.log(`- 战略规划: ${hasStrategicPlan ? '✅' : '❌'}`);

        const allElementsPresent = hasGoalDecomposition && hasStateAwareness && 
                                 hasPathOptimization && hasAdaptiveAdjustment && 
                                 hasDecisionTemplate && hasStrategicPlan;

        console.log(`\n🎯 动态规划提示词工程状态: ${allElementsPresent ? '✅ 成功实施' : '❌ 需要改进'}`);

        if (allElementsPresent) {
            console.log('\n🎉 恭喜！动态规划提示词工程已成功实施！');
            console.log('AI助手现在具备了：');
            console.log('- 🎯 目标导向的决策能力');
            console.log('- 🧭 状态感知和路径优化能力');
            console.log('- 🔄 自适应调整和动态规划能力');
            console.log('- 📚 基于system_guide.md的知识应用能力');
        } else {
            console.log('\n⚠️ 动态规划提示词工程需要进一步完善');
        }

        return {
            success: allElementsPresent,
            analysis,
            promptConfig,
            elements: {
                hasGoalDecomposition,
                hasStateAwareness,
                hasPathOptimization,
                hasAdaptiveAdjustment,
                hasDecisionTemplate,
                hasStrategicPlan
            }
        };

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        return {
            success: false,
            error: error.message
        };
    }
}

// 运行测试
if (require.main === module) {
    testDynamicPlanning()
        .then(result => {
            console.log('\n📊 测试完成！');
            if (result.success) {
                console.log('🎉 动态规划提示词工程测试通过！');
                process.exit(0);
            } else {
                console.log('❌ 动态规划提示词工程测试失败');
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('💥 测试过程中发生错误:', error);
            process.exit(1);
        });
}

module.exports = { testDynamicPlanning };
