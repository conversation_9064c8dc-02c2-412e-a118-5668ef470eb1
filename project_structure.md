# 项目结构设计

## 1. 整体目录结构
```
project_RPA/
├── docs/                           # 文档目录
│   ├── PRD.md                     # 产品需求文档
│   ├── architecture.md           # 架构设计文档
│   ├── database_design.md        # 数据库设计文档
│   └── concurrent_architecture.md # 并发架构设计文档
├── src/                           # 源代码目录
│   ├── shared/                    # 共享模块
│   │   ├── config/               # 配置管理
│   │   ├── database/             # 数据库相关
│   │   ├── utils/                # 工具函数
│   │   └── types/                # TypeScript类型定义
│   ├── workorder-system/         # 工单系统
│   │   ├── backend/              # 后端服务
│   │   │   ├── src/
│   │   │   │   ├── controllers/  # 控制器
│   │   │   │   ├── services/     # 业务逻辑
│   │   │   │   ├── models/       # 数据模型
│   │   │   │   ├── routes/       # 路由定义
│   │   │   │   ├── middleware/   # 中间件
│   │   │   │   └── websocket/    # WebSocket处理
│   │   │   ├── package.json
│   │   │   └── server.js
│   │   └── frontend/             # 前端应用
│   │       ├── src/
│   │       │   ├── components/   # Vue组件
│   │       │   ├── views/        # 页面视图
│   │       │   ├── store/        # 状态管理
│   │       │   ├── api/          # API调用
│   │       │   ├── utils/        # 工具函数
│   │       │   └── assets/       # 静态资源
│   │       ├── public/
│   │       ├── package.json
│   │       └── vite.config.js
│   ├── ai-assistant/             # AI助手系统
│   │   ├── src/
│   │   │   ├── core/             # 核心引擎
│   │   │   │   ├── orchestrator.js    # 流程控制器
│   │   │   │   ├── task-builder.js    # 任务构建器
│   │   │   │   ├── worker-pool.js     # 工作线程池
│   │   │   │   └── scheduler.js       # 任务调度器
│   │   │   ├── models/           # 模型集成
│   │   │   │   ├── main-model.js      # 主模型调用
│   │   │   │   ├── vl-model.js        # VL模型调用
│   │   │   │   └── model-manager.js   # 模型管理器
│   │   │   ├── browser/          # 浏览器自动化
│   │   │   │   ├── browser-pool.js    # 浏览器实例池
│   │   │   │   ├── page-controller.js # 页面控制器
│   │   │   │   ├── action-executor.js # 动作执行器
│   │   │   │   └── screenshot.js      # 截图功能
│   │   │   ├── workers/          # 工作线程
│   │   │   │   ├── ai-worker.js       # AI工作线程
│   │   │   │   └── worker-manager.js  # 线程管理器
│   │   │   ├── communication/    # 通信模块
│   │   │   │   ├── websocket-client.js # WebSocket客户端
│   │   │   │   └── message-handler.js  # 消息处理器
│   │   │   ├── ui/               # 用户界面
│   │   │   │   ├── cui-interface.js   # CUI界面
│   │   │   │   ├── chat-component.js  # 聊天组件
│   │   │   │   └── status-display.js  # 状态显示
│   │   │   └── utils/            # 工具函数
│   │   │       ├── image-processor.js # 图片处理
│   │   │       ├── file-manager.js    # 文件管理
│   │   │       └── logger.js          # 日志记录
│   │   ├── data/                 # 数据存储
│   │   │   ├── database.db       # SQLite数据库
│   │   │   ├── screenshots/      # 截图存储
│   │   │   └── logs/             # 日志文件
│   │   ├── package.json
│   │   └── app.js
│   └── prompt/                   # 提示词模板
│       ├── main_model.md         # 主模型提示词
│       └── vl_model.md           # VL模型提示词
├── config/                       # 配置文件
│   ├── .env.example             # 环境变量模板
│   └── database.sql             # 数据库初始化脚本
├── scripts/                     # 脚本文件
│   ├── setup.sh                 # 环境设置脚本
│   ├── start-dev.sh             # 开发环境启动脚本
│   └── build.sh                 # 构建脚本
├── tests/                       # 测试文件
│   ├── unit/                    # 单元测试
│   ├── integration/             # 集成测试
│   └── e2e/                     # 端到端测试
├── system_guide.md              # 操作指引文档
├── .env                         # 环境变量(不提交到git)
├── .gitignore                   # Git忽略文件
├── package.json                 # 根package.json
├── README.md                    # 项目说明
└── docker-compose.yml           # Docker编排文件(可选)
```

## 2. 技术栈选择

### 2.1 后端技术栈
- **运行时**: Node.js 18+
- **Web框架**: Express.js
- **数据库**: SQLite3
- **ORM**: 无(使用原生SQL，保持轻量)
- **WebSocket**: ws库
- **浏览器自动化**: Playwright
- **图片处理**: Sharp
- **日志**: Winston
- **进程管理**: PM2(生产环境)

### 2.2 前端技术栈
- **框架**: Vue.js 3
- **构建工具**: Vite
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router
- **HTTP客户端**: Axios
- **富文本编辑器**: TinyMCE或Quill
- **WebSocket客户端**: 原生WebSocket API

### 2.3 开发工具
- **包管理器**: npm
- **代码规范**: ESLint + Prettier
- **类型检查**: TypeScript(可选)
- **测试框架**: Jest + Vue Test Utils
- **API文档**: Swagger/OpenAPI

## 3. 启动脚本设计

### 3.1 package.json (根目录)
```json
{
  "name": "rpa-automation-platform",
  "version": "1.0.0",
  "description": "RPA自动化平台",
  "scripts": {
    "install:all": "npm install && npm run install:workorder && npm run install:ai",
    "install:workorder": "cd src/workorder-system/backend && npm install && cd ../frontend && npm install",
    "install:ai": "cd src/ai-assistant && npm install",
    "dev": "concurrently \"npm run dev:workorder-backend\" \"npm run dev:workorder-frontend\" \"npm run dev:ai-assistant\"",
    "dev:workorder-backend": "cd src/workorder-system/backend && npm run dev",
    "dev:workorder-frontend": "cd src/workorder-system/frontend && npm run dev",
    "dev:ai-assistant": "cd src/ai-assistant && npm run dev",
    "build": "npm run build:workorder && npm run build:ai",
    "build:workorder": "cd src/workorder-system/frontend && npm run build",
    "build:ai": "cd src/ai-assistant && npm run build",
    "start": "concurrently \"npm run start:workorder\" \"npm run start:ai\"",
    "start:workorder": "cd src/workorder-system/backend && npm start",
    "start:ai": "cd src/ai-assistant && npm start",
    "test": "npm run test:workorder && npm run test:ai",
    "test:workorder": "cd src/workorder-system && npm test",
    "test:ai": "cd src/ai-assistant && npm test",
    "setup": "node scripts/setup.js"
  },
  "devDependencies": {
    "concurrently": "^7.6.0"
  }
}
```

### 3.2 环境变量配置
```bash
# .env.example
# 数据库配置
DATABASE_PATH=./src/ai-assistant/data/database.db

# 大模型配置
DASHSCOPE_API_KEY=sk-7416a845a80443db82afc35951e804e7
DASHSCOPE_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
MAIN_MODEL=qwen-turbo
VL_MODEL=qwen-vl-plus-2025-01-25

# 服务端口配置
WORKORDER_BACKEND_PORT=3001
WORKORDER_FRONTEND_PORT=3000
AI_ASSISTANT_PORT=3002

# WebSocket配置
WEBSOCKET_PORT=3003

# 并发配置
MAX_CONCURRENT_WORKERS=5
MIN_CONCURRENT_WORKERS=2
BROWSER_POOL_SIZE=8

# 文件存储配置
SCREENSHOT_DIR=./src/ai-assistant/data/screenshots
LOG_DIR=./src/ai-assistant/data/logs

# 开发模式配置
NODE_ENV=development
LOG_LEVEL=debug
```