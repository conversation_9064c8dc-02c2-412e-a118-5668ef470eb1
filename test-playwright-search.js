#!/usr/bin/env node

/**
 * Playwright搜索测试 - 展示完整的搜索流程
 * 使用Playwright的强大原生能力完成真实的搜索操作
 */

require('dotenv').config();
const { chromium } = require('playwright');

async function testPlaywrightSearch() {
    console.log('🔍 Playwright搜索测试开始');
    console.log('展示Playwright原生能力完成完整搜索流程');
    console.log('='.repeat(50));
    
    let browser = null;
    let page = null;
    
    try {
        // 1. 启动浏览器
        console.log('🌐 启动浏览器...');
        browser = await chromium.launch({
            headless: false,
            slowMo: 1000,
            args: ['--start-maximized']
        });
        
        page = await browser.newPage();
        await page.setViewportSize({ width: 1920, height: 1080 });
        
        // 2. 导航到百度
        console.log('\n📍 导航到百度首页...');
        await page.goto('https://www.baidu.com', {
            waitUntil: 'domcontentloaded',
            timeout: 30000
        });
        
        // 等待页面完全加载
        await page.waitForLoadState('networkidle');
        console.log('✅ 页面加载完成');
        
        // 3. 使用多种Playwright方法查找搜索框
        console.log('\n🎯 使用Playwright原生方法查找搜索框...');
        
        let searchInput = null;
        
        // 方法1: 通过ID查找
        console.log('🔍 方法1: 通过ID查找搜索框...');
        try {
            searchInput = page.locator('#kw');
            const count = await searchInput.count();
            console.log(`   找到 ${count} 个ID为kw的元素`);
            
            if (count > 0 && await searchInput.isVisible()) {
                console.log('✅ 通过ID成功找到搜索框');
            } else {
                searchInput = null;
            }
        } catch (e) {
            console.log('❌ 通过ID查找失败');
        }
        
        // 方法2: 通过name属性查找
        if (!searchInput) {
            console.log('🔍 方法2: 通过name属性查找搜索框...');
            try {
                searchInput = page.locator('[name="wd"]');
                const count = await searchInput.count();
                console.log(`   找到 ${count} 个name为wd的元素`);
                
                if (count > 0 && await searchInput.isVisible()) {
                    console.log('✅ 通过name属性成功找到搜索框');
                } else {
                    searchInput = null;
                }
            } catch (e) {
                console.log('❌ 通过name属性查找失败');
            }
        }
        
        // 方法3: 通过placeholder查找
        if (!searchInput) {
            console.log('🔍 方法3: 通过placeholder查找搜索框...');
            try {
                searchInput = page.getByPlaceholder(/搜索|search/i);
                const count = await searchInput.count();
                console.log(`   找到 ${count} 个包含搜索placeholder的元素`);
                
                if (count > 0 && await searchInput.first().isVisible()) {
                    searchInput = searchInput.first();
                    console.log('✅ 通过placeholder成功找到搜索框');
                } else {
                    searchInput = null;
                }
            } catch (e) {
                console.log('❌ 通过placeholder查找失败');
            }
        }
        
        // 方法4: 通过role查找
        if (!searchInput) {
            console.log('🔍 方法4: 通过role查找搜索框...');
            try {
                searchInput = page.getByRole('textbox');
                const count = await searchInput.count();
                console.log(`   找到 ${count} 个textbox角色的元素`);
                
                if (count > 0 && await searchInput.first().isVisible()) {
                    searchInput = searchInput.first();
                    console.log('✅ 通过role成功找到搜索框');
                } else {
                    searchInput = null;
                }
            } catch (e) {
                console.log('❌ 通过role查找失败');
            }
        }
        
        // 方法5: 通用input查找
        if (!searchInput) {
            console.log('🔍 方法5: 查找所有input元素...');
            try {
                const allInputs = page.locator('input[type="text"], input:not([type])');
                const count = await allInputs.count();
                console.log(`   找到 ${count} 个input元素`);
                
                for (let i = 0; i < count; i++) {
                    const input = allInputs.nth(i);
                    const isVisible = await input.isVisible();
                    const placeholder = await input.getAttribute('placeholder') || '';
                    const name = await input.getAttribute('name') || '';
                    const id = await input.getAttribute('id') || '';
                    
                    console.log(`   Input ${i + 1}: visible=${isVisible}, placeholder="${placeholder}", name="${name}", id="${id}"`);
                    
                    if (isVisible && (id === 'kw' || name === 'wd' || placeholder.includes('搜索'))) {
                        searchInput = input;
                        console.log(`✅ 找到搜索框: Input ${i + 1}`);
                        break;
                    }
                }
            } catch (e) {
                console.log('❌ 通用input查找失败');
            }
        }
        
        if (!searchInput) {
            console.log('❌ 所有方法都无法找到搜索框');
            return;
        }
        
        // 4. 在搜索框中输入文本
        console.log('\n📝 在搜索框中输入文本...');
        const searchText = 'Playwright自动化测试';
        
        // 确保搜索框可见并获得焦点
        await searchInput.scrollIntoViewIfNeeded();
        await searchInput.click();
        
        // 清空并输入文本
        await searchInput.fill('');
        await searchInput.type(searchText, { delay: 100 });
        
        console.log(`✅ 成功输入文本: "${searchText}"`);
        
        // 验证输入
        const inputValue = await searchInput.inputValue();
        console.log(`🔍 验证输入值: "${inputValue}"`);
        
        if (inputValue === searchText) {
            console.log('✅ 输入验证成功');
        } else {
            console.log('❌ 输入验证失败');
        }
        
        // 5. 查找并点击搜索按钮
        console.log('\n🔘 查找搜索按钮...');
        
        let searchButton = null;
        
        // 方法1: 通过文本查找
        try {
            searchButton = page.getByText('百度一下');
            const count = await searchButton.count();
            console.log(`   通过文本找到 ${count} 个"百度一下"按钮`);
            
            if (count > 0 && await searchButton.first().isVisible()) {
                searchButton = searchButton.first();
                console.log('✅ 通过文本成功找到搜索按钮');
            } else {
                searchButton = null;
            }
        } catch (e) {
            console.log('❌ 通过文本查找按钮失败');
        }
        
        // 方法2: 通过ID查找
        if (!searchButton) {
            try {
                searchButton = page.locator('#su');
                const count = await searchButton.count();
                console.log(`   通过ID找到 ${count} 个搜索按钮`);
                
                if (count > 0 && await searchButton.isVisible()) {
                    console.log('✅ 通过ID成功找到搜索按钮');
                } else {
                    searchButton = null;
                }
            } catch (e) {
                console.log('❌ 通过ID查找按钮失败');
            }
        }
        
        // 方法3: 通过role查找
        if (!searchButton) {
            try {
                searchButton = page.getByRole('button', { name: /百度一下|搜索/i });
                const count = await searchButton.count();
                console.log(`   通过role找到 ${count} 个搜索按钮`);
                
                if (count > 0 && await searchButton.first().isVisible()) {
                    searchButton = searchButton.first();
                    console.log('✅ 通过role成功找到搜索按钮');
                } else {
                    searchButton = null;
                }
            } catch (e) {
                console.log('❌ 通过role查找按钮失败');
            }
        }
        
        if (!searchButton) {
            console.log('❌ 无法找到搜索按钮，尝试按Enter键');
            await searchInput.press('Enter');
        } else {
            // 点击搜索按钮
            console.log('🖱️ 点击搜索按钮...');
            await searchButton.click();
            console.log('✅ 成功点击搜索按钮');
        }
        
        // 6. 等待搜索结果页面加载
        console.log('\n⏳ 等待搜索结果页面加载...');
        
        try {
            // 等待URL变化
            await page.waitForURL(/.*s\?.*wd=.*/, { timeout: 10000 });
            console.log('✅ URL已变化，搜索请求已发送');
            
            // 等待页面稳定
            await page.waitForLoadState('networkidle');
            console.log('✅ 搜索结果页面加载完成');
            
            // 分析搜索结果页面
            const newUrl = page.url();
            const newTitle = await page.title();
            
            console.log(`📊 搜索结果页面信息:`);
            console.log(`   URL: ${newUrl}`);
            console.log(`   标题: ${newTitle}`);
            
            // 统计搜索结果
            const resultLinks = page.locator('.result h3 a, .c-container h3 a');
            const resultCount = await resultLinks.count();
            console.log(`   搜索结果数量: ${resultCount}`);
            
            if (resultCount > 0) {
                console.log('✅ 搜索成功，找到搜索结果');
                
                // 显示前几个搜索结果
                console.log('\n📋 前5个搜索结果:');
                for (let i = 0; i < Math.min(5, resultCount); i++) {
                    try {
                        const link = resultLinks.nth(i);
                        const text = await link.textContent();
                        const href = await link.getAttribute('href');
                        console.log(`   ${i + 1}. ${text?.trim() || '无标题'}`);
                        console.log(`      链接: ${href || '无链接'}`);
                    } catch (e) {
                        console.log(`   ${i + 1}. 无法获取结果信息`);
                    }
                }
            } else {
                console.log('⚠️ 未找到搜索结果');
            }
            
        } catch (e) {
            console.log('❌ 等待搜索结果超时或失败:', e.message);
        }
        
        // 7. 截图保存结果
        console.log('\n📸 截图保存搜索结果...');
        
        const screenshot = await page.screenshot({
            fullPage: false,
            type: 'png'
        });
        
        console.log('✅ 截图完成');
        console.log(`📊 截图信息:`);
        console.log(`   大小: ${screenshot.length} 字节`);
        console.log(`   时间: ${new Date().toISOString()}`);
        console.log(`   页面: ${page.url()}`);
        
        console.log('\n🎉 Playwright搜索测试完成!');
        console.log('✨ 成功展示了Playwright的强大能力:');
        console.log('   - 多种元素定位策略 (ID, name, placeholder, role, 通用选择器)');
        console.log('   - 智能等待机制 (waitForURL, waitForLoadState, networkidle)');
        console.log('   - 真实用户交互 (click, type, fill, press)');
        console.log('   - 页面状态检测 (isVisible, isEnabled, count)');
        console.log('   - 自动滚动和聚焦 (scrollIntoViewIfNeeded)');
        console.log('   - 结果验证和分析 (inputValue, textContent, getAttribute)');
        
    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error.message);
        console.error('错误堆栈:', error.stack);
    } finally {
        if (browser) {
            console.log('\n🔚 关闭浏览器...');
            await browser.close();
        }
    }
}

// 运行测试
if (require.main === module) {
    testPlaywrightSearch().catch(console.error);
}

module.exports = { testPlaywrightSearch };
