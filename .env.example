# RPA自动化平台环境变量配置示例
# 复制此文件为 .env 并填入实际配置值

# 阿里云百炼模型配置
DASHSCOPE_API_KEY=sk-7416a845a80443db82afc35951e804e7
BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

# AI模型配置
MAIN_MODEL=qwen-plus
VL_MODEL=qwen-vl-plus

# 服务端口配置
WORKORDER_PORT=3001
AI_ASSISTANT_PORT=3002
FRONTEND_PORT=3000

# 数据库配置
DATABASE_PATH=src/ai-assistant/data/database.db

# 日志配置
LOG_LEVEL=info
LOG_TO_FILE=true
LOG_TO_CONSOLE=true

# 浏览器配置
BROWSER_HEADLESS=true
BROWSER_POOL_SIZE=5
MAX_WORKERS=1

# WebSocket配置
WS_HEARTBEAT_INTERVAL=30000
WS_RECONNECT_INTERVAL=5000
WS_MAX_RECONNECT_ATTEMPTS=10

# 任务配置
MAX_TASK_DURATION=600000
TASK_RETRY_DELAY=5000
MAX_TASK_RETRIES=3

# 图片处理配置
IMAGE_MAX_WIDTH=800
IMAGE_MAX_HEIGHT=600
IMAGE_QUALITY=80

# 安全配置
ENABLE_CORS=true
CORS_ORIGIN=http://localhost:3000

# 开发模式配置
NODE_ENV=development
DEBUG=false
