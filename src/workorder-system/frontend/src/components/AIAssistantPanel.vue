<template>
  <div class="ai-assistant-panel">
    <el-card class="panel-card">
      <template #header>
        <div class="panel-header">
          <h3>AI助手</h3>
          <el-tag :type="statusType" size="small">
            {{ statusText }}
          </el-tag>
        </div>
      </template>

      <div class="panel-content">
        <!-- AI助手状态 -->
        <div class="status-section">
          <el-descriptions :column="2" size="small" border>
            <el-descriptions-item label="运行状态">
              <el-tag :type="statusType" size="small">
                {{ statusText }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="工作线程">
              {{ aiStatus.workers || 0 }} 个
            </el-descriptions-item>
            <el-descriptions-item label="队列任务">
              {{ aiStatus.queue_size || 0 }} 个
            </el-descriptions-item>
            <el-descriptions-item label="处理速度">
              {{ processingRate }} 任务/分钟
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 控制按钮 -->
        <div class="control-section">
          <el-button-group>
            <el-button
              v-if="!['online', 'busy', 'idle'].includes(aiStatus.status)"
              type="primary"
              :loading="starting"
              @click="startAI"
            >
              启动AI助手
            </el-button>
            <el-button
              v-if="['online', 'busy', 'idle'].includes(aiStatus.status)"
              type="danger"
              :loading="stopping"
              @click="stopAI"
            >
              停止AI助手
            </el-button>
            <el-button @click="refreshStatus">
              刷新状态
            </el-button>
          </el-button-group>
        </div>

        <!-- 最近任务 -->
        <div class="recent-tasks">
          <h4>最近任务</h4>
          <div v-if="recentTasks.length > 0">
            <el-timeline size="small">
              <el-timeline-item
                v-for="task in recentTasks"
                :key="task.id"
                :timestamp="formatTime(task.timestamp)"
                :type="getTaskType(task.status)"
              >
                <div class="task-item">
                  <span class="task-title">{{ task.title }}</span>
                  <el-tag :type="getTaskType(task.status)" size="small">
                    {{ task.status }}
                  </el-tag>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
          <div v-else class="empty-state">
            <el-empty description="暂无任务历史" :image-size="60" />
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useSystemStore } from '@/store/system'
import { useWebSocketStore } from '@/store/websocket'

const systemStore = useSystemStore()
const wsStore = useWebSocketStore()

// 响应式数据
const starting = ref(false)
const stopping = ref(false)
const recentTasks = ref([])
const refreshTimer = ref(null)

// 计算属性
const aiStatus = computed(() => systemStore.systemStatus.ai_assistant || {})

const statusType = computed(() => {
  switch (aiStatus.value.status) {
    case 'online': return 'success'
    case 'busy': return 'warning'
    case 'idle': return 'info'
    case 'error': return 'danger'
    default: return 'info'
  }
})

const statusText = computed(() => {
  switch (aiStatus.value.status) {
    case 'online': return '在线'
    case 'busy': return '忙碌'
    case 'idle': return '空闲'
    case 'error': return '错误'
    case 'offline': return '离线'
    default: return '未知'
  }
})

const processingRate = computed(() => {
  // 简单的处理速度计算
  return Math.round(Math.random() * 10) // 模拟数据
})

// 方法
const startAI = async () => {
  starting.value = true
  try {
    await systemStore.startAIAssistant()
    ElMessage.success('AI助手启动成功')

    // 延迟刷新状态，确保服务完全启动
    setTimeout(async () => {
      await refreshStatus()
    }, 1000)

  } catch (error) {
    ElMessage.error('启动AI助手失败: ' + error.message)
  } finally {
    starting.value = false
  }
}

const stopAI = async () => {
  stopping.value = true
  try {
    await systemStore.stopAIAssistant()
    ElMessage.success('AI助手已停止')

    // 延迟刷新状态，确保服务完全停止
    setTimeout(async () => {
      await refreshStatus()
    }, 1000)

  } catch (error) {
    ElMessage.error('停止AI助手失败: ' + error.message)
  } finally {
    stopping.value = false
  }
}

const refreshStatus = async () => {
  try {
    // 刷新系统状态
    await systemStore.loadSystemStatus()

    // 刷新最近任务
    await loadRecentTasks()

    // 强制更新AI状态显示
    aiStatus.value = { ...systemStore.systemStatus.ai_assistant }

    console.log('✅ AI助手状态已刷新:', aiStatus.value)
  } catch (error) {
    console.error('❌ 刷新状态失败:', error)
    ElMessage.error('刷新状态失败: ' + error.message)
  }
}

const loadRecentTasks = async () => {
  try {
    // 从AI助手服务获取真实的最近任务数据
    const response = await fetch('http://localhost:3002/recent-tasks?limit=5')
    if (response.ok) {
      const result = await response.json()
      if (result.success) {
        recentTasks.value = result.data
      } else {
        console.warn('获取最近任务失败:', result.message)
        recentTasks.value = []
      }
    } else {
      console.warn('AI助手服务不可用，显示空任务列表')
      recentTasks.value = []
    }
  } catch (error) {
    console.warn('获取最近任务失败:', error.message)
    // 如果AI助手服务不可用，显示空列表而不是模拟数据
    recentTasks.value = []
  }
}

const getTaskType = (status) => {
  switch (status) {
    case '已完成': return 'success'
    case '进行中': return 'primary'
    case '失败': return 'danger'
    default: return 'info'
  }
}

const formatTime = (date) => {
  return new Date(date).toLocaleTimeString()
}

// 生命周期
onMounted(async () => {
  await refreshStatus()

  // 监听WebSocket执行步骤更新
  wsStore.addEventListener('execution_step_update', handleExecutionStepUpdate)

  // 减少定期刷新频率，因为现在有实时推送
  refreshTimer.value = setInterval(refreshStatus, 60000) // 改为60秒
})

onUnmounted(() => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
  }

  // 移除WebSocket事件监听
  wsStore.removeEventListener('execution_step_update', handleExecutionStepUpdate)
})

// WebSocket事件处理
const handleExecutionStepUpdate = (data) => {
  console.log('🔄 收到实时执行步骤更新:', data)

  // 将新步骤添加到列表前面
  if (data && data.id) {
    // 检查是否已存在，避免重复
    const existingIndex = recentTasks.value.findIndex(task => task.id === data.id)
    if (existingIndex === -1) {
      recentTasks.value.unshift(data)

      // 保持列表长度限制
      if (recentTasks.value.length > 10) {
        recentTasks.value = recentTasks.value.slice(0, 10)
      }

      console.log('✅ 实时添加执行步骤:', data.title)
    }
  }
}
</script>

<style scoped>
.ai-assistant-panel {
  height: 100%;
}

.panel-card {
  height: 100%;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  margin: 0;
  color: #303133;
}

.panel-content {
  height: calc(100% - 60px);
  overflow-y: auto;
}

.status-section {
  margin-bottom: 20px;
}

.control-section {
  margin-bottom: 20px;
  text-align: center;
}

.recent-tasks h4 {
  margin: 0 0 15px 0;
  color: #606266;
  font-size: 14px;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-title {
  color: #303133;
  font-size: 14px;
}

.empty-state {
  text-align: center;
  margin-top: 40px;
}
</style>