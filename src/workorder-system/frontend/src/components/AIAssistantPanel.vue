<template>
  <div class="ai-assistant-panel">
    <el-card class="panel-card">
      <template #header>
        <div class="panel-header">
          <h3>AI助手</h3>
          <el-tag :type="statusType" size="small">
            {{ statusText }}
          </el-tag>
        </div>
      </template>

      <div class="panel-content">
        <!-- AI助手状态 -->
        <div class="status-section">
          <el-descriptions :column="2" size="small" border>
            <el-descriptions-item label="运行状态">
              <el-tag :type="statusType" size="small">
                {{ statusText }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="工作线程">
              {{ aiStatus.workers || 0 }} 个
            </el-descriptions-item>
            <el-descriptions-item label="队列任务">
              {{ aiStatus.queue_size || 0 }} 个
            </el-descriptions-item>
            <el-descriptions-item label="处理速度">
              {{ processingRate }} 任务/分钟
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 控制按钮 -->
        <div class="control-section">
          <el-button-group>
            <el-button
              v-if="!['online', 'busy', 'idle'].includes(aiStatus.status)"
              type="primary"
              :loading="starting"
              @click="startAI"
            >
              启动AI助手
            </el-button>
            <el-button
              v-if="['online', 'busy', 'idle'].includes(aiStatus.status)"
              type="danger"
              :loading="stopping"
              @click="stopAI"
            >
              停止AI助手
            </el-button>
            <el-button @click="refreshStatus">
              刷新状态
            </el-button>
          </el-button-group>
        </div>

        <!-- AI执行步骤 -->
        <div class="recent-tasks">
          <h4>AI执行步骤</h4>
          <div v-if="recentTasks.length > 0">
            <el-timeline size="small">
              <el-timeline-item
                v-for="step in recentTasks"
                :key="step.id"
                :timestamp="formatTime(step.timestamp)"
                :type="getStepType(step.status)"
                :icon="getStepIcon(step.tool)"
              >
                <div class="step-item">
                  <div class="step-header">
                    <span class="step-title">{{ step.title }}</span>
                    <el-tag :type="getStepType(step.status)" size="small">
                      {{ getStepStatusText(step.status) }}
                    </el-tag>
                  </div>
                  <div class="step-details" v-if="step.description">
                    <span class="step-description">{{ step.description }}</span>
                  </div>
                  <div class="step-meta" v-if="step.tool || step.element">
                    <el-tag size="mini" type="info" v-if="step.tool">{{ step.tool }}</el-tag>
                    <el-tag size="mini" type="warning" v-if="step.element && step.element !== 'N/A'">{{ step.element }}</el-tag>
                  </div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
          <div v-else class="empty-state">
            <el-empty description="AI助手暂无执行步骤" :image-size="60" />
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useSystemStore } from '@/store/system'

const systemStore = useSystemStore()

// 响应式数据
const starting = ref(false)
const stopping = ref(false)
const recentTasks = ref([])
const refreshTimer = ref(null)

// 计算属性
const aiStatus = computed(() => systemStore.systemStatus.ai_assistant || {})

const statusType = computed(() => {
  switch (aiStatus.value.status) {
    case 'online': return 'success'
    case 'busy': return 'warning'
    case 'idle': return 'info'
    case 'error': return 'danger'
    default: return 'info'
  }
})

const statusText = computed(() => {
  switch (aiStatus.value.status) {
    case 'online': return '在线'
    case 'busy': return '忙碌'
    case 'idle': return '空闲'
    case 'error': return '错误'
    case 'offline': return '离线'
    default: return '未知'
  }
})

const processingRate = computed(() => {
  // 简单的处理速度计算
  return Math.round(Math.random() * 10) // 模拟数据
})

// 方法
const startAI = async () => {
  starting.value = true
  try {
    await systemStore.startAIAssistant()
    ElMessage.success('AI助手启动成功')

    // 延迟刷新状态，确保服务完全启动
    setTimeout(async () => {
      await refreshStatus()
    }, 1000)

  } catch (error) {
    ElMessage.error('启动AI助手失败: ' + error.message)
  } finally {
    starting.value = false
  }
}

const stopAI = async () => {
  stopping.value = true
  try {
    await systemStore.stopAIAssistant()
    ElMessage.success('AI助手已停止')

    // 延迟刷新状态，确保服务完全停止
    setTimeout(async () => {
      await refreshStatus()
    }, 1000)

  } catch (error) {
    ElMessage.error('停止AI助手失败: ' + error.message)
  } finally {
    stopping.value = false
  }
}

const refreshStatus = async () => {
  try {
    // 刷新系统状态
    await systemStore.loadSystemStatus()

    // 刷新最近任务
    await loadRecentTasks()

    // 强制更新AI状态显示
    aiStatus.value = { ...systemStore.systemStatus.ai_assistant }

    console.log('✅ AI助手状态已刷新:', aiStatus.value)
  } catch (error) {
    console.error('❌ 刷新状态失败:', error)
    ElMessage.error('刷新状态失败: ' + error.message)
  }
}

const loadRecentTasks = async () => {
  try {
    // 从AI助手服务获取实时执行步骤
    const response = await fetch('http://localhost:3002/recent-tasks?limit=10')
    if (response.ok) {
      const result = await response.json()
      if (result.success) {
        recentTasks.value = result.data
        console.log('✅ 获取AI执行步骤成功:', result.data.length, '条')
      } else {
        console.warn('获取AI执行步骤失败:', result.message)
        recentTasks.value = []
      }
    } else {
      console.warn('AI助手服务不可用，显示空步骤列表')
      recentTasks.value = []
    }
  } catch (error) {
    console.warn('获取AI执行步骤失败:', error.message)
    recentTasks.value = []
  }
}

const getStepType = (status) => {
  switch (status) {
    case 'completed': return 'success'
    case 'processing': return 'primary'
    case 'failed': return 'danger'
    case 'error': return 'danger'
    default: return 'info'
  }
}

const getStepStatusText = (status) => {
  switch (status) {
    case 'completed': return '已完成'
    case 'processing': return '执行中'
    case 'failed': return '失败'
    case 'error': return '错误'
    default: return '未知'
  }
}

const getStepIcon = (tool) => {
  switch (tool) {
    case 'browser_navigate': return 'Link'
    case 'browser_click': return 'Mouse'
    case 'browser_type': return 'Edit'
    case 'browser_snapshot': return 'Camera'
    case 'browser_wait_for': return 'Clock'
    default: return 'Operation'
  }
}

const formatTime = (date) => {
  return new Date(date).toLocaleTimeString()
}

// 生命周期
onMounted(async () => {
  await refreshStatus()

  // 定期刷新状态 - 更频繁地刷新以显示实时步骤
  refreshTimer.value = setInterval(refreshStatus, 5000) // 5秒刷新一次
})

onUnmounted(() => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
  }
})
</script>

<style scoped>
.ai-assistant-panel {
  height: 100%;
}

.panel-card {
  height: 100%;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  margin: 0;
  color: #303133;
}

.panel-content {
  height: calc(100% - 60px);
  overflow-y: auto;
}

.status-section {
  margin-bottom: 20px;
}

.control-section {
  margin-bottom: 20px;
  text-align: center;
}

.recent-tasks h4 {
  margin: 0 0 15px 0;
  color: #606266;
  font-size: 14px;
}

.step-item {
  padding: 8px 0;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.step-title {
  font-size: 13px;
  color: #303133;
  font-weight: 500;
  flex: 1;
  margin-right: 10px;
}

.step-details {
  margin-bottom: 4px;
}

.step-description {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.step-meta {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.empty-state {
  text-align: center;
  margin-top: 40px;
}
</style>