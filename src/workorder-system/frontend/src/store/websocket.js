import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useTicketsStore } from './tickets'

export const useWebSocketStore = defineStore('websocket', () => {
  // 状态
  const ws = ref(null)
  const connected = ref(false)
  const connecting = ref(false)
  const error = ref(null)
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = ref(5)
  const reconnectInterval = ref(3000) // 3秒
  const heartbeatInterval = ref(30000) // 30秒心跳
  const heartbeatTimer = ref(null)
  const reconnectTimer = ref(null)

  // 消息队列
  const messageQueue = ref([])
  const lastMessage = ref(null)

  // 事件监听器
  const eventListeners = ref(new Map())

  // 计算属性
  const connectionStatus = computed(() => {
    if (connecting.value) return 'connecting'
    if (connected.value) return 'connected'
    if (error.value) return 'error'
    return 'disconnected'
  })

  const canReconnect = computed(() => {
    return reconnectAttempts.value < maxReconnectAttempts.value
  })

  // WebSocket URL
  const getWebSocketUrl = () => {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const host = window.location.hostname
    const port = import.meta.env.VITE_WS_PORT || '3001'
    return `${protocol}//${host}:${port}/ws`
  }

  // 连接WebSocket
  const connect = () => {
    if (ws.value && (ws.value.readyState === WebSocket.CONNECTING || ws.value.readyState === WebSocket.OPEN)) {
      return
    }

    connecting.value = true
    error.value = null

    try {
      const wsUrl = getWebSocketUrl()
      console.log('正在连接WebSocket:', wsUrl)
      
      ws.value = new WebSocket(wsUrl)

      ws.value.onopen = handleOpen
      ws.value.onmessage = handleMessage
      ws.value.onclose = handleClose
      ws.value.onerror = handleError

    } catch (err) {
      console.error('WebSocket连接失败:', err)
      error.value = err.message
      connecting.value = false
    }
  }

  // 断开连接
  const disconnect = () => {
    if (heartbeatTimer.value) {
      clearInterval(heartbeatTimer.value)
      heartbeatTimer.value = null
    }

    if (reconnectTimer.value) {
      clearTimeout(reconnectTimer.value)
      reconnectTimer.value = null
    }

    if (ws.value) {
      ws.value.close()
      ws.value = null
    }

    connected.value = false
    connecting.value = false
    reconnectAttempts.value = 0
  }

  // 发送消息
  const send = (message) => {
    if (!connected.value || !ws.value) {
      console.warn('WebSocket未连接，消息已加入队列')
      messageQueue.value.push(message)
      return false
    }

    try {
      const messageStr = typeof message === 'string' ? message : JSON.stringify(message)
      ws.value.send(messageStr)
      return true
    } catch (err) {
      console.error('发送消息失败:', err)
      error.value = err.message
      return false
    }
  }

  // 发送心跳
  const sendHeartbeat = () => {
    send({
      type: 'heartbeat',
      timestamp: Date.now()
    })
  }

  // 处理连接打开
  const handleOpen = () => {
    console.log('WebSocket连接成功')
    connected.value = true
    connecting.value = false
    reconnectAttempts.value = 0
    error.value = null

    // 发送队列中的消息
    while (messageQueue.value.length > 0) {
      const message = messageQueue.value.shift()
      send(message)
    }

    // 启动心跳
    if (heartbeatTimer.value) {
      clearInterval(heartbeatTimer.value)
    }
    heartbeatTimer.value = setInterval(sendHeartbeat, heartbeatInterval.value)

    // 发送连接确认
    send({
      type: 'client_connected',
      timestamp: Date.now(),
      client_type: 'workorder_frontend'
    })
  }

  // 处理消息
  const handleMessage = (event) => {
    try {
      const message = JSON.parse(event.data)
      lastMessage.value = message

      console.log('收到WebSocket消息:', message)

      // 触发消息事件
      emit('message', message)

      // 根据消息类型处理
      switch (message.type) {
        case 'ticket_updated':
          handleTicketUpdate(message.data)
          break
        case 'ticket_created':
          handleTicketCreated(message.data)
          break
        case 'ticket_deleted':
          handleTicketDeleted(message.data)
          break
        case 'system_status':
          handleSystemStatus(message.data)
          break
        case 'execution_step_update':
          handleExecutionStepUpdate(message.data)
          break
        case 'heartbeat_response':
          // 心跳响应，无需处理
          break
        default:
          console.log('未知消息类型:', message.type)
      }
    } catch (err) {
      console.error('解析WebSocket消息失败:', err)
    }
  }

  // 处理连接关闭
  const handleClose = (event) => {
    console.log('WebSocket连接关闭:', event.code, event.reason)
    connected.value = false
    connecting.value = false

    if (heartbeatTimer.value) {
      clearInterval(heartbeatTimer.value)
      heartbeatTimer.value = null
    }

    // 如果不是主动关闭，尝试重连
    if (event.code !== 1000 && canReconnect.value) {
      scheduleReconnect()
    }
  }

  // 处理连接错误
  const handleError = (event) => {
    console.error('WebSocket错误:', event)
    error.value = 'WebSocket连接错误'
    connecting.value = false
  }

  // 安排重连
  const scheduleReconnect = () => {
    if (!canReconnect.value) {
      console.log('已达到最大重连次数，停止重连')
      return
    }

    reconnectAttempts.value++
    const delay = reconnectInterval.value * Math.pow(2, reconnectAttempts.value - 1) // 指数退避
    
    console.log(`${delay}ms后尝试第${reconnectAttempts.value}次重连`)
    
    reconnectTimer.value = setTimeout(() => {
      if (!connected.value) {
        connect()
      }
    }, delay)
  }

  // 处理工单更新
  const handleTicketUpdate = (ticketData) => {
    const ticketsStore = useTicketsStore()
    const index = ticketsStore.tickets.findIndex(ticket => ticket.id === ticketData.id)
    if (index !== -1) {
      ticketsStore.tickets[index] = { ...ticketsStore.tickets[index], ...ticketData }
    }
  }

  // 处理工单创建
  const handleTicketCreated = (ticketData) => {
    const ticketsStore = useTicketsStore()
    ticketsStore.tickets.unshift(ticketData)
  }

  // 处理工单删除
  const handleTicketDeleted = (ticketData) => {
    const ticketsStore = useTicketsStore()
    ticketsStore.tickets = ticketsStore.tickets.filter(ticket => ticket.id !== ticketData.id)
  }

  // 处理系统状态
  const handleSystemStatus = (statusData) => {
    console.log('系统状态更新:', statusData)
    // 可以在这里更新系统状态store
  }

  // 处理执行步骤更新
  const handleExecutionStepUpdate = (stepData) => {
    console.log('收到执行步骤更新:', stepData)
    // 触发execution_step_update事件，让AI助手面板监听
    emit('execution_step_update', stepData)
  }

  // 重置重连计数
  const resetReconnectAttempts = () => {
    reconnectAttempts.value = 0
  }

  // 事件监听器管理
  const on = (event, callback) => {
    if (!eventListeners.value.has(event)) {
      eventListeners.value.set(event, [])
    }
    eventListeners.value.get(event).push(callback)
  }

  const off = (event, callback) => {
    if (eventListeners.value.has(event)) {
      const listeners = eventListeners.value.get(event)
      const index = listeners.indexOf(callback)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  const emit = (event, data) => {
    if (eventListeners.value.has(event)) {
      eventListeners.value.get(event).forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('事件监听器执行错误:', error)
        }
      })
    }
  }

  return {
    // 状态
    connected,
    connecting,
    error,
    reconnectAttempts,
    maxReconnectAttempts,
    messageQueue,
    lastMessage,
    
    // 计算属性
    connectionStatus,
    canReconnect,
    
    // Actions
    connect,
    disconnect,
    send,
    resetReconnectAttempts,
    on,
    off,
    emit
  }
})
