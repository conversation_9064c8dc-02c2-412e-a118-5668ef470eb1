<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RPA平台服务启动器</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .service-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: #fafafa;
        }
        .service-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        .service-info {
            margin-bottom: 15px;
            color: #666;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 10px;
        }
        .status-running {
            background: #d4edda;
            color: #155724;
        }
        .status-stopped {
            background: #f8d7da;
            color: #721c24;
        }
        .status-checking {
            background: #d1ecf1;
            color: #0c5460;
        }
        button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #0056CC;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .ai-test-section {
            background: #e8f5e8;
            border: 2px solid #28a745;
        }
        .test-result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            white-space: pre-wrap;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 RPA自动化平台服务启动器</h1>
            <p>管理和启动工单系统、AI助手等服务</p>
        </div>

        <!-- 工单系统后端 -->
        <div class="service-section">
            <div class="service-title">
                📊 工单系统后端服务
                <span id="workorderStatus" class="status status-checking">检查中...</span>
            </div>
            <div class="service-info">
                端口: 3001 | 路径: src/workorder-system/backend/server.js
            </div>
            <button onclick="startWorkorderBackend()">启动后端服务</button>
            <button onclick="checkWorkorderStatus()">检查状态</button>
            <div id="workorderLog" class="log" style="display: none;"></div>
        </div>

        <!-- AI助手服务 -->
        <div class="service-section">
            <div class="service-title">
                🤖 AI助手服务
                <span id="aiStatus" class="status status-checking">检查中...</span>
            </div>
            <div class="service-info">
                端口: 3002 | 路径: src/ai-assistant/server.js
            </div>
            <button onclick="startAIAssistant()">启动AI助手</button>
            <button onclick="checkAIStatus()">检查状态</button>
            <div id="aiLog" class="log" style="display: none;"></div>
        </div>

        <!-- AI助手测试 -->
        <div class="service-section ai-test-section">
            <div class="service-title">
                🧪 AI助手工单解析测试
                <span id="testStatus" class="status status-stopped">未开始</span>
            </div>
            <div class="service-info">
                测试门店: CAFÉ E.S.KIMO 小泉居(威翠店)（12268）
            </div>
            <button onclick="runAITest()">🚀 运行AI解析测试</button>
            <button onclick="runDirectAITest()">🎯 直接测试AI模型</button>
            <div id="testResult" class="test-result" style="display: none;"></div>
        </div>

        <!-- 服务状态总览 -->
        <div class="service-section">
            <div class="service-title">📈 服务状态总览</div>
            <button onclick="checkAllServices()">🔍 检查所有服务</button>
            <button onclick="startAllServices()">🚀 启动所有服务</button>
            <div id="overallLog" class="log" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 服务状态管理
        const services = {
            workorder: { port: 3001, name: '工单系统后端' },
            ai: { port: 3002, name: 'AI助手服务' }
        };

        // 检查服务状态
        async function checkServiceStatus(port) {
            try {
                const response = await fetch(`http://localhost:${port}/health`, {
                    method: 'GET',
                    timeout: 3000
                });
                return response.ok;
            } catch (error) {
                return false;
            }
        }

        // 更新状态显示
        function updateStatus(elementId, isRunning) {
            const element = document.getElementById(elementId);
            if (isRunning) {
                element.textContent = '运行中';
                element.className = 'status status-running';
            } else {
                element.textContent = '已停止';
                element.className = 'status status-stopped';
            }
        }

        // 显示日志
        function showLog(elementId, message) {
            const logElement = document.getElementById(elementId);
            logElement.style.display = 'block';
            logElement.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            logElement.scrollTop = logElement.scrollHeight;
        }

        // 工单系统后端相关函数
        async function checkWorkorderStatus() {
            const isRunning = await checkServiceStatus(3001);
            updateStatus('workorderStatus', isRunning);
            showLog('workorderLog', `工单系统后端状态: ${isRunning ? '运行中' : '已停止'}`);
        }

        function startWorkorderBackend() {
            showLog('workorderLog', '正在启动工单系统后端...');
            showLog('workorderLog', '请在终端中运行: cd src/workorder-system/backend && npm start');
            showLog('workorderLog', '或者运行: npm run start:workorder');
            
            // 模拟启动过程
            setTimeout(() => {
                showLog('workorderLog', '提示: 如果终端有问题，请手动在新终端窗口中运行启动命令');
            }, 2000);
        }

        // AI助手相关函数
        async function checkAIStatus() {
            const isRunning = await checkServiceStatus(3002);
            updateStatus('aiStatus', isRunning);
            showLog('aiLog', `AI助手服务状态: ${isRunning ? '运行中' : '已停止'}`);
        }

        function startAIAssistant() {
            showLog('aiLog', '正在启动AI助手服务...');
            showLog('aiLog', '请在终端中运行: cd src/ai-assistant && npm start');
            showLog('aiLog', '或者运行: npm run start:ai');
            
            setTimeout(() => {
                showLog('aiLog', '提示: 确保已设置DASHSCOPE_API_KEY环境变量');
            }, 2000);
        }

        // AI测试相关函数
        async function runDirectAITest() {
            const testStatus = document.getElementById('testStatus');
            const testResult = document.getElementById('testResult');
            
            testStatus.textContent = '测试中...';
            testStatus.className = 'status status-checking';
            testResult.style.display = 'block';
            testResult.className = 'test-result';
            testResult.textContent = '🔄 正在调用AI模型进行工单解析测试...\n';

            try {
                // 测试工单
                const testTicket = {
                    title: '下架CAFÉ E.S.KIMO小泉居威翠店招牌咖啡',
                    content: `请帮我下架门店"CAFÉ E.S.KIMO 小泉居(威翠店)（12268）"的外卖商品"招牌咖啡"。

要求：
1. 使用BD商户后台
2. 按照标准流程操作
3. 完成后截图确认`
                };

                // 系统提示词
                const systemPrompt = `你是一个专业的RPA（机器人流程自动化）AI助手，负责理解用户的工单需求并生成具体的自动化任务。

## 标准操作流程（基于system_guide.md）
1. 打开BD商户后台 (https://uat-merchant.aomiapp.com/#/bdlogin)
2. 等待用户完成登录
3. 搜索门店（使用门店ID或名称）
4. 点击查询，进入商户管理后台
5. 系统管理 → 门店管理
6. 点击进入门店
7. 商品管理 → 外卖商品管理
8. 搜索商品（按名称或ID）
9. 点击下架
10. 二次确认
11. 截图保存

请生成JSON格式的任务列表，包含具体的操作步骤。`;

                const messages = [
                    { role: 'system', content: systemPrompt },
                    { 
                        role: 'user', 
                        content: `请解析以下工单内容：\n\n标题: ${testTicket.title}\n内容: ${testTicket.content}`
                    }
                ];

                const response = await fetch('https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer sk-7416a845a80443db82afc35951e804e7'
                    },
                    body: JSON.stringify({
                        model: 'qwen-turbo',
                        messages: messages,
                        temperature: 0.7,
                        max_tokens: 2000
                    })
                });

                if (!response.ok) {
                    throw new Error(`API调用失败: ${response.status}`);
                }

                const result = await response.json();
                
                if (result.choices && result.choices[0]) {
                    const aiContent = result.choices[0].message.content;
                    
                    let resultText = `✅ AI模型调用成功！\n\n`;
                    resultText += `🤖 AI回复:\n${aiContent}\n\n`;
                    
                    // 验证关键内容
                    const checks = [
                        { name: 'BD后台地址', check: aiContent.includes('uat-merchant.aomiapp.com') },
                        { name: '门店ID 12268', check: aiContent.includes('12268') },
                        { name: '商品名称', check: aiContent.includes('咖啡') },
                        { name: '下架操作', check: aiContent.includes('下架') },
                        { name: '截图确认', check: aiContent.includes('截图') }
                    ];
                    
                    resultText += `🔍 关键内容验证:\n`;
                    let passedChecks = 0;
                    checks.forEach(check => {
                        const status = check.check ? '✅' : '❌';
                        resultText += `   ${status} ${check.name}\n`;
                        if (check.check) passedChecks++;
                    });
                    
                    resultText += `\n📈 验证结果: ${passedChecks}/${checks.length} 项通过\n`;
                    
                    if (passedChecks >= 4) {
                        resultText += `\n🎉 AI助手测试通过！能够正确理解工单需求\n`;
                        testStatus.textContent = '测试通过';
                        testStatus.className = 'status status-running';
                        testResult.className = 'test-result success';
                    } else {
                        resultText += `\n⚠️ AI助手测试部分通过，需要优化\n`;
                        testStatus.textContent = '部分通过';
                        testStatus.className = 'status status-checking';
                        testResult.className = 'test-result';
                    }
                    
                    testResult.textContent = resultText;
                } else {
                    throw new Error('AI响应格式异常');
                }

            } catch (error) {
                let errorText = `❌ AI测试失败: ${error.message}\n\n`;
                
                if (error.message.includes('401')) {
                    errorText += `💡 可能原因: API密钥无效\n`;
                } else if (error.message.includes('fetch')) {
                    errorText += `💡 可能原因: 网络连接问题\n`;
                }
                
                errorText += `\n🔧 解决方案:\n`;
                errorText += `1. 检查网络连接\n`;
                errorText += `2. 验证API密钥\n`;
                errorText += `3. 尝试启动本地服务进行测试\n`;
                
                testResult.textContent = errorText;
                testResult.className = 'test-result error';
                testStatus.textContent = '测试失败';
                testStatus.className = 'status status-stopped';
            }
        }

        function runAITest() {
            showLog('overallLog', '启动AI助手测试...');
            showLog('overallLog', '请确保AI助手服务已启动 (端口3002)');
            showLog('overallLog', '如果服务未启动，请先点击"直接测试AI模型"');
        }

        // 检查所有服务
        async function checkAllServices() {
            const overallLog = document.getElementById('overallLog');
            overallLog.style.display = 'block';
            overallLog.textContent = '';
            
            showLog('overallLog', '开始检查所有服务状态...');
            
            for (const [key, service] of Object.entries(services)) {
                const isRunning = await checkServiceStatus(service.port);
                showLog('overallLog', `${service.name} (端口${service.port}): ${isRunning ? '✅ 运行中' : '❌ 已停止'}`);
            }
            
            showLog('overallLog', '服务状态检查完成');
        }

        function startAllServices() {
            showLog('overallLog', '准备启动所有服务...');
            showLog('overallLog', '');
            showLog('overallLog', '请在终端中依次运行以下命令:');
            showLog('overallLog', '');
            showLog('overallLog', '1. 启动工单系统后端:');
            showLog('overallLog', '   cd src/workorder-system/backend && npm start');
            showLog('overallLog', '');
            showLog('overallLog', '2. 启动AI助手服务:');
            showLog('overallLog', '   cd src/ai-assistant && npm start');
            showLog('overallLog', '');
            showLog('overallLog', '3. 或者使用项目根目录的快捷命令:');
            showLog('overallLog', '   npm run start:workorder');
            showLog('overallLog', '   npm run start:ai');
            showLog('overallLog', '');
            showLog('overallLog', '提示: 如果终端有问题，请手动在新终端窗口中运行');
        }

        // 页面加载时检查服务状态
        window.onload = function() {
            setTimeout(() => {
                checkWorkorderStatus();
                checkAIStatus();
            }, 1000);
        };
    </script>
</body>
</html>
