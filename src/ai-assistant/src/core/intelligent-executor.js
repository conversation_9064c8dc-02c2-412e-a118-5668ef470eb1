/**
 * 智能执行控制器
 * 使用AI驱动的判断逻辑，完全消除硬编码
 */

const { AITaskAnalyzer } = require('./ai-task-analyzer');
const { DynamicPromptGenerator } = require('./dynamic-prompt-generator');
const { NotificationService } = require('../services/notification-service');
const EnhancedValidationEngine = require('./enhanced-validation-engine');
const logger = require('../utils/logger');
const axios = require('axios');

class IntelligentExecutor {
    constructor(mcpClient, config = {}, aiController = null) {
        this.mcpClient = mcpClient;
        this.aiController = aiController; // 添加AI控制器引用，用于检查挂起状态
        this.taskAnalyzer = new AITaskAnalyzer();
        this.promptGenerator = new DynamicPromptGenerator();
        this.notificationService = new NotificationService(logger);

        // 初始化增强验证引擎
        this.validationEngine = new EnhancedValidationEngine(config);
        this.config = config;

        // AI模型配置
        this.baseUrl = process.env.DASHSCOPE_BASE_URL || 'https://dashscope.aliyuncs.com/compatible-mode/v1';
        this.model = process.env.MAIN_MODEL || 'qwen-turbo';
        this.apiKey = process.env.DASHSCOPE_API_KEY;

        // 循环检测机制 - 降低敏感度
        this.operationHistory = [];
        this.maxHistorySize = 15;
        this.loopDetectionThreshold = 5; // 从3提高到5，减少误判
        this.milestoneCheckInterval = 8; // 从5提高到8，给更多时间
        this.lastMilestoneIteration = 0;

        // 持续执行机制
        this.consecutiveNoProgressCount = 0;
        this.maxNoProgressBeforeReplan = 3;
        this.forceProgressAfterIterations = 10;
        this.triggerSelfReview = false;
    }

    /**
     * 初始化智能执行器
     */
    async initialize() {
        try {
            await this.validationEngine.initialize();
            logger.info('✅ 智能执行器初始化完成');
        } catch (error) {
            logger.error('❌ 智能执行器初始化失败:', error.message);
            throw error;
        }
    }

    /**
     * 智能执行RPA任务
     * @param {Object} ticket - 工单信息
     * @returns {Object} 执行结果
     */
    async executeTask(ticket) {
        try {
            logger.info(`🧠 开始智能执行任务: ${ticket.id}`);
            
            // 第1步：AI分析任务
            const analysis = await this.taskAnalyzer.analyzeTask(ticket.content, {
                ticketId: ticket.id,
                title: ticket.title,
                priority: ticket.priority
            });

            // 第2步：动态生成提示词
            const promptConfig = this.promptGenerator.generatePrompt(analysis, ticket);

            // 第3步：执行AI驱动的自动化
            const result = await this.executeWithIntelligentControl(
                promptConfig, 
                analysis, 
                ticket
            );

            logger.info(`✅ 智能执行完成: ${ticket.id}`);
            return result;

        } catch (error) {
            logger.error(`❌ 智能执行失败: ${ticket.id}`, error);
            return {
                success: false,
                error: error.message,
                analysis: null,
                iterations: 0
            };
        }
    }

    /**
     * 执行AI驱动的智能控制循环
     */
    async executeWithIntelligentControl(promptConfig, analysis, ticket) {
        const maxIterations = this.calculateMaxIterations(analysis, promptConfig.maxIterations);
        const executionHistory = [];
        let iteration = 0;

        // 初始化对话历史
        const conversationHistory = [
            {
                role: 'system',
                content: promptConfig.systemPrompt
            },
            {
                role: 'user',
                content: promptConfig.taskPrompt
            }
        ];

        logger.info(`🔄 开始智能控制循环，最大迭代次数: ${maxIterations} (基于复杂度: ${analysis.complexityLevel})`);

        while (iteration < maxIterations) {
            iteration++;
            logger.info(`🔄 智能迭代 ${iteration}/${maxIterations}`);

            // 检查里程碑进展
            this.checkMilestoneProgress(iteration);

            // 检查工单是否被挂起
            if (this.aiController && this.aiController.isTicketSuspended && this.aiController.isTicketSuspended(ticket.id)) {
                logger.info(`🛑 工单 ${ticket.id} 已被挂起，停止执行`);
                return {
                    success: false,
                    error: '工单已被挂起',
                    suspended: true,
                    analysis: analysis,
                    executionHistory: executionHistory,
                    iterations: iteration
                };
            }

            try {
                // 调用AI模型
                const aiResponse = await this.callAIModel(conversationHistory, promptConfig.tools);
                
                if (!aiResponse) {
                    logger.warn('⚠️ AI响应为空，跳过此次迭代');
                    continue;
                }

                // 记录执行历史
                executionHistory.push({
                    iteration,
                    aiResponse: aiResponse.content,
                    toolCalls: aiResponse.tool_calls || [],
                    timestamp: new Date().toISOString()
                });

                // 处理工具调用
                if (aiResponse.tool_calls && aiResponse.tool_calls.length > 0) {
                    const toolResults = await this.executeTools(aiResponse.tool_calls);

                    // 记录操作历史用于循环检测
                    for (const toolCall of aiResponse.tool_calls) {
                        const pageUrl = this.getCurrentPageUrl(toolResults);
                        const elementRef = toolCall.function?.arguments ?
                            JSON.parse(toolCall.function.arguments).ref : null;

                        this.recordOperation(pageUrl, toolCall.function.name, elementRef);

                        // 检测循环操作
                        if (this.detectLoop(pageUrl, toolCall.function.name, elementRef)) {
                            logger.warn(`🔄 检测到循环操作，尝试恢复策略`);
                            const strategies = this.getLoopRecoveryStrategy(pageUrl, toolCall.function.name);

                            // 添加循环恢复提示到对话历史
                            conversationHistory.push({
                                role: 'user',
                                content: `检测到循环操作！请采用以下恢复策略之一：${strategies.join('; ')}`
                            });
                        }
                    }

                    // 添加工具调用结果到对话历史
                    conversationHistory.push({
                        role: 'assistant',
                        content: aiResponse.content || '',
                        tool_calls: aiResponse.tool_calls
                    });

                    for (const result of toolResults) {
                        conversationHistory.push({
                            role: 'tool',
                            tool_call_id: result.tool_call_id,
                            content: JSON.stringify(result)
                        });
                    }

                    // 更新执行历史
                    executionHistory[executionHistory.length - 1].toolResults = toolResults;
                } else {
                    // 没有工具调用，添加AI响应到对话历史
                    conversationHistory.push({
                        role: 'assistant',
                        content: aiResponse.content
                    });

                    // 使用增强验证引擎进行多维度验证
                    const validation = await this.validationEngine.validateCompletion(
                        aiResponse.content,
                        ticket,
                        executionHistory
                    );

                    logger.info('🔍 AI验证结果:', validation);

                    // 根据验证结果决定下一步
                    if (validation.isCompleted && validation.confidence > 0.8) {
                        logger.info('✅ AI确认任务真正完成');
                        return {
                            success: true,
                            finalMessage: aiResponse.content,
                            analysis: analysis,
                            validation: validation,
                            executionHistory: executionHistory,
                            iterations: iteration
                        };
                    }

                    // 检查是否需要用户干预
                    if (validation.requiresUserIntervention) {
                        const interventionResult = await this.handleUserIntervention(
                            validation,
                            ticket,
                            aiResponse.content
                        );

                        if (!interventionResult.success) {
                            return {
                                success: false,
                                error: '用户干预失败或超时',
                                analysis: analysis,
                                validation: validation,
                                executionHistory: executionHistory,
                                iterations: iteration
                            };
                        }

                        // 用户干预完成，添加继续提示
                        conversationHistory.push({
                            role: 'user',
                            content: this.promptGenerator.generateContinuePrompt(analysis, '用户干预已完成')
                        });
                    } else {
                        // 添加继续执行的提示
                        conversationHistory.push({
                            role: 'user',
                            content: this.promptGenerator.generateContinuePrompt(analysis, aiResponse.content)
                        });
                    }
                }

            } catch (error) {
                logger.error(`❌ 智能迭代 ${iteration} 失败:`, error);
                
                // 智能错误分析和恢复
                const shouldRetry = await this.analyzeErrorAndDecideRetry(error, iteration, maxIterations);

                if (shouldRetry) {
                    const waitTime = this.calculateRetryWaitTime(iteration);
                    logger.info(`🔄 检测到可恢复错误，等待 ${waitTime}ms 后重试...`);
                    await this.sleep(waitTime);
                    continue;
                }

                logger.error(`💥 不可恢复的错误，停止执行`);
                return {
                    success: false,
                    error: error.message,
                    analysis: analysis,
                    executionHistory: executionHistory,
                    iterations: iteration
                };
            }
        }

        // 达到最大迭代次数
        logger.warn(`⏰ 达到最大迭代次数 ${maxIterations}，任务可能未完成`);
        
        // 使用增强验证引擎进行最终验证
        const finalValidation = await this.validationEngine.validateCompletion(
            '达到最大迭代次数',
            ticket,
            executionHistory
        );

        return {
            success: false,
            error: `达到最大迭代次数 ${maxIterations}，任务未完成`,
            analysis: analysis,
            validation: finalValidation,
            executionHistory: executionHistory,
            iterations: iteration
        };
    }

    /**
     * 根据任务复杂度计算最大迭代次数
     */
    calculateMaxIterations(analysis, defaultMax = 15) {
        const complexityMap = {
            'low': 10,
            'medium': 20,
            'high': 30,
            'complex': 40
        };

        const baseIterations = complexityMap[analysis.complexityLevel] || defaultMax;

        // 根据预估步骤数调整
        const stepAdjustment = Math.ceil((analysis.estimatedSteps || 5) * 2);

        // 根据是否需要登录调整
        const loginAdjustment = analysis.requiresLogin ? 5 : 0;

        // 根据是否需要用户输入调整
        const userInputAdjustment = analysis.requiresUserInput ? 3 : 0;

        const finalIterations = Math.max(
            baseIterations + stepAdjustment + loginAdjustment + userInputAdjustment,
            10 // 最少10次迭代
        );

        logger.info(`🧮 迭代次数计算: 基础(${baseIterations}) + 步骤(${stepAdjustment}) + 登录(${loginAdjustment}) + 用户输入(${userInputAdjustment}) = ${finalIterations}`);

        return Math.min(finalIterations, 50); // 最多50次迭代
    }

    /**
     * 调用AI模型
     */
    async callAIModel(conversationHistory, tools) {
        const response = await axios.post(`${this.baseUrl}/chat/completions`, {
            model: this.model,
            messages: conversationHistory,
            tools: tools,
            tool_choice: 'auto',
            temperature: 0.1,
            max_tokens: 2000
        }, {
            headers: {
                'Authorization': `Bearer ${this.apiKey}`,
                'Content-Type': 'application/json'
            }
        });

        return response.data.choices[0].message;
    }

    /**
     * 执行工具调用
     */
    async executeTools(toolCalls) {
        const results = [];

        for (const toolCall of toolCalls) {
            try {
                const toolName = toolCall.function.name.replace('_Playwright', '');
                const args = JSON.parse(toolCall.function.arguments);

                logger.info(`🛠️ 执行工具: ${toolName}`, args);

                const result = await this.mcpClient.callTool(toolName, args);
                
                results.push({
                    tool_call_id: toolCall.id,
                    success: true,
                    tool: toolCall.function.name,
                    result: result,
                    timestamp: new Date().toISOString()
                });

            } catch (error) {
                logger.error(`❌ 工具执行失败: ${toolCall.function.name}`, error);
                
                results.push({
                    tool_call_id: toolCall.id,
                    success: false,
                    tool: toolCall.function.name,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
            }
        }

        return results;
    }

    /**
     * 处理用户干预
     */
    async handleUserIntervention(validation, ticket, aiResponse) {
        try {
            logger.info('🚨 需要用户干预:', validation.interventionType);

            // 发送通知
            await this.notificationService.sendUserInterventionNotification({
                ticketId: ticket.id,
                title: '需要用户操作',
                message: validation.reasoning,
                type: validation.interventionType,
                url: validation.suggestedAction
            });

            // 等待用户处理
            const maxWaitTime = validation.maxWaitTime || 300000; // 默认5分钟
            const completed = await this.notificationService.waitForUserIntervention(
                ticket.id,
                maxWaitTime
            );

            return { success: completed };

        } catch (error) {
            logger.error('❌ 用户干预处理失败:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 智能错误分析和重试决策
     */
    async analyzeErrorAndDecideRetry(error, iteration, maxIterations) {
        // 如果已经接近最大迭代次数，不再重试
        if (iteration >= maxIterations - 2) {
            logger.info('🚫 接近最大迭代次数，不再重试');
            return false;
        }

        // 网络相关错误 - 可重试
        const networkErrors = [
            'ERR_BAD_RESPONSE',
            'ECONNREFUSED',
            'ENOTFOUND',
            'ETIMEDOUT',
            'ECONNRESET'
        ];

        if (networkErrors.some(errType =>
            error.code === errType ||
            error.message.includes(errType) ||
            (error.status && error.status >= 500)
        )) {
            logger.info('🔄 网络错误，可以重试');
            return true;
        }

        // API限流错误 - 可重试
        if (error.status === 429 || error.message.includes('rate limit')) {
            logger.info('🔄 API限流错误，可以重试');
            return true;
        }

        // 临时服务错误 - 可重试
        if (error.status === 502 || error.status === 503 || error.status === 504) {
            logger.info('🔄 临时服务错误，可以重试');
            return true;
        }

        // 其他错误 - 不重试
        logger.info('🚫 不可恢复的错误，不重试');
        return false;
    }

    /**
     * 计算重试等待时间（指数退避）
     */
    calculateRetryWaitTime(iteration) {
        // 指数退避：1秒, 2秒, 4秒, 8秒, 最多16秒
        const baseWait = 1000;
        const maxWait = 16000;
        const waitTime = Math.min(baseWait * Math.pow(2, iteration - 1), maxWait);

        // 添加随机抖动，避免雷群效应
        const jitter = Math.random() * 1000;

        return waitTime + jitter;
    }

    /**
     * 睡眠函数
     */
    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 记录操作历史
     */
    recordOperation(pageUrl, operationType, elementRef = null) {
        const operation = {
            timestamp: Date.now(),
            pageUrl,
            operationType,
            elementRef,
            iteration: this.operationHistory.length + 1
        };

        this.operationHistory.push(operation);

        // 保持历史记录大小
        if (this.operationHistory.length > this.maxHistorySize) {
            this.operationHistory.shift();
        }

        logger.debug(`📝 记录操作: ${operationType} on ${pageUrl} (${elementRef || 'N/A'})`);
    }

    /**
     * 优化的循环检测 - 降低敏感度，防止误判
     */
    detectLoop(currentPageUrl, currentOperation, currentElementRef = null) {
        if (this.operationHistory.length < this.loopDetectionThreshold) {
            return false;
        }

        // 检查最近的操作是否重复
        const recentOperations = this.operationHistory.slice(-this.loopDetectionThreshold);
        const sameOperations = recentOperations.filter(op =>
            op.pageUrl === currentPageUrl &&
            op.operationType === currentOperation &&
            op.elementRef === currentElementRef
        );

        // 只有在连续相同操作且确实没有进展时才认为是循环
        const isActualLoop = sameOperations.length >= this.loopDetectionThreshold &&
                           this.consecutiveNoProgressCount >= 2;

        if (isActualLoop) {
            logger.warn(`🔄 检测到真正的循环: ${currentOperation} on ${currentElementRef} at ${currentPageUrl}`);
            logger.warn(`🔄 无进展次数: ${this.consecutiveNoProgressCount}, 相同操作次数: ${sameOperations.length}`);
            return true;
        }

        // 如果只是重复操作但可能有进展，不认为是循环
        if (sameOperations.length >= this.loopDetectionThreshold) {
            logger.info(`⚠️ 检测到重复操作，但可能仍有进展，继续执行...`);
        }

        return false;
    }

    /**
     * 检查里程碑进展
     */
    checkMilestoneProgress(currentIteration) {
        if (currentIteration - this.lastMilestoneIteration >= this.milestoneCheckInterval) {
            logger.info(`📊 里程碑检查: 迭代 ${currentIteration}, 上次里程碑: ${this.lastMilestoneIteration}`);

            // 这里可以添加更复杂的进展检查逻辑
            // 比如检查页面URL是否有变化，是否达成了新的业务状态等

            this.lastMilestoneIteration = currentIteration;
            return true;
        }
        return false;
    }

    /**
     * 获取循环检测建议
     */
    getLoopRecoveryStrategy(pageUrl, operationType) {
        const strategies = [
            '重新获取页面快照，分析当前状态',
            '尝试不同的元素定位方式',
            '检查是否需要等待页面加载完成',
            '考虑使用备选操作路径',
            '请求用户干预或报告问题'
        ];

        logger.info(`💡 循环恢复策略建议: ${strategies.join(', ')}`);
        return strategies;
    }

    /**
     * 从工具结果中提取当前页面URL
     */
    getCurrentPageUrl(toolResults) {
        for (const result of toolResults) {
            if (result.content && typeof result.content === 'string') {
                try {
                    const content = JSON.parse(result.content);
                    if (content.pageUrl) {
                        return content.pageUrl;
                    }
                } catch (e) {
                    // 如果不是JSON，尝试从文本中提取URL
                    const urlMatch = result.content.match(/Page URL: (.+)/);
                    if (urlMatch) {
                        return urlMatch[1].trim();
                    }
                }
            }
        }
        return 'unknown';
    }
}

module.exports = { IntelligentExecutor };
