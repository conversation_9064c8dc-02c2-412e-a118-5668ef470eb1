/**
 * AI驱动的任务分析器
 * 使用AI来理解任务内容，而不是硬编码规则
 */

const axios = require('axios');
const logger = require('../utils/logger');

class AITaskAnalyzer {
    constructor() {
        this.baseUrl = 'https://dashscope.aliyuncs.com/compatible-mode/v1';
        this.model = 'qwen-turbo';
        this.apiKey = process.env.DASHSCOPE_API_KEY;
        this.systemGuide = null;
    }

    /**
     * 加载系统指南
     */
    async loadSystemGuide() {
        try {
            const fs = require('fs').promises;
            const path = require('path');
            const guidePath = path.join(__dirname, '../../../../system_guide.md');
            this.systemGuide = await fs.readFile(guidePath, 'utf-8');
            logger.info('✅ AI任务分析器系统指南加载成功');
        } catch (error) {
            logger.error('❌ AI任务分析器系统指南加载失败:', error);
            this.systemGuide = '';
        }
    }

    /**
     * AI驱动的任务分析
     * @param {string} taskContent - 任务内容
     * @param {Object} context - 上下文信息
     * @returns {Object} 任务分析结果
     */
    async analyzeTask(taskContent, context = {}) {
        try {
            // 确保系统指南已加载
            if (!this.systemGuide) {
                await this.loadSystemGuide();
            }

            const analysisPrompt = this.buildAnalysisPrompt(taskContent, context);
            
            const response = await axios.post(`${this.baseUrl}/chat/completions`, {
                model: this.model,
                messages: [
                    {
                        role: 'system',
                        content: '你是一个专业的RPA任务分析专家。请分析用户的任务需求，返回结构化的JSON分析结果。'
                    },
                    {
                        role: 'user',
                        content: analysisPrompt
                    }
                ],
                temperature: 0.1,
                max_tokens: 1000
            }, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                }
            });

            const aiResponse = response.data.choices[0].message.content;
            const analysis = this.parseAnalysisResult(aiResponse);
            
            logger.info('🧠 AI任务分析完成:', analysis);
            return analysis;

        } catch (error) {
            logger.error('❌ AI任务分析失败:', error.message);
            return this.getFallbackAnalysis(taskContent);
        }
    }

    /**
     * 构建任务分析提示词
     */
    buildAnalysisPrompt(taskContent, context) {
        return `
你是一个具备动态规划能力的RPA自动化任务分析器。你的核心能力是：
1. **目标分解**：将复杂任务分解为可执行的子目标
2. **状态感知**：准确理解当前页面状态和业务上下文
3. **路径优化**：选择最短路径达成目标
4. **自适应调整**：根据实际情况动态调整策略

## 执行原则
1. **永远以最终目标为导向**
2. **每步操作都要最大化接近目标**
3. **遇到障碍时要灵活调整路径**
4. **充分利用system_guide.md的指导**

任务内容：${taskContent}

系统操作指南：
${this.systemGuide || '暂无系统指南'}

上下文信息：${JSON.stringify(context, null, 2)}

请返回以下格式的JSON分析，重点关注动态规划策略：
{
    "taskType": "任务类型(web_automation/data_entry/file_processing/api_testing/general)",
    "targetWebsite": "推测的目标网站URL(如果是web任务)",
    "keyEntities": ["关键实体1", "关键实体2"],
    "actionVerbs": ["主要动作1", "主要动作2"],
    "completionCriteria": ["完成标准1", "完成标准2"],
    "estimatedSteps": 预估步骤数,
    "complexityLevel": "复杂度(low/medium/high)",
    "requiresLogin": true/false,
    "requiresUserInput": true/false,
    "riskLevel": "风险等级(low/medium/high)",
    "suggestedTimeout": 预估超时时间(秒),
    "dependencies": ["依赖项1", "依赖项2"],
    "strategicPlan": {
        "finalGoal": "最终目标描述",
        "keyMilestones": ["关键里程碑1", "关键里程碑2"],
        "criticalPath": ["关键路径步骤1", "关键路径步骤2"],
        "adaptationPoints": ["需要动态调整的节点1", "需要动态调整的节点2"]
    }
}

## 动态规划指导原则：
1. **目标导向分析**：明确最终目标，识别关键里程碑
2. **路径规划**：基于系统指南设计最优执行路径
3. **状态转换**：每个操作都应该让系统更接近目标状态
4. **适应性设计**：识别可能需要动态调整的关键节点
5. **验证机制**：为每个里程碑设计验证标准

注意：
1. 请仔细阅读系统操作指南，从中提取正确的URL地址和操作流程
2. 如果任务涉及BD商户后台，请使用系统指南中的正确地址
3. 基于系统指南中的步骤来估算任务复杂度和步骤数
4. 重点关注业务流程的逻辑顺序和依赖关系
5. 识别可能的异常情况和恢复策略
6. 返回纯JSON格式，不要包含其他文本
`;
    }

    /**
     * 解析AI分析结果
     */
    parseAnalysisResult(aiResponse) {
        try {
            // 提取JSON部分
            const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                return JSON.parse(jsonMatch[0]);
            }
            
            // 如果没有找到JSON，尝试直接解析
            return JSON.parse(aiResponse);
        } catch (error) {
            logger.warn('⚠️ AI分析结果解析失败，使用默认分析');
            return this.getFallbackAnalysis(aiResponse);
        }
    }

    /**
     * 获取回退分析结果
     */
    getFallbackAnalysis(taskContent) {
        // 智能识别任务类型
        const taskType = this.intelligentTaskTypeDetection(taskContent);

        // 基于任务类型生成更准确的分析
        const baseAnalysis = {
            taskType: taskType,
            targetWebsite: this.extractTargetWebsite(taskContent, taskType),
            keyEntities: this.extractKeyEntities(taskContent),
            actionVerbs: this.extractActionVerbs(taskContent),
            completionCriteria: this.generateCompletionCriteria(taskContent, taskType),
            estimatedSteps: this.estimateSteps(taskContent, taskType),
            complexityLevel: this.assessComplexity(taskContent, taskType),
            requiresLogin: this.checkLoginRequirement(taskContent),
            requiresUserInput: this.checkUserInputRequirement(taskContent),
            riskLevel: 'low',
            suggestedTimeout: 300,
            dependencies: []
        };

        // 如果是web_automation任务，添加战略规划
        if (taskType === 'web_automation') {
            baseAnalysis.strategicPlan = this.generateStrategicPlan(taskContent);
        }

        return baseAnalysis;
    }

    /**
     * 智能任务类型检测
     */
    intelligentTaskTypeDetection(taskContent) {
        const content = taskContent.toLowerCase();

        // Web自动化关键词
        const webKeywords = ['下架', '上架', '登录', '点击', '输入', '搜索', '商户', '门店', '商品', '管理后台', 'bd', '小泉居'];
        const webScore = webKeywords.filter(keyword => content.includes(keyword)).length;

        if (webScore >= 2) {
            return 'web_automation';
        }

        // 数据处理关键词
        const dataKeywords = ['导入', '导出', '数据', '表格', '文件', '处理'];
        const dataScore = dataKeywords.filter(keyword => content.includes(keyword)).length;

        if (dataScore >= 1) {
            return 'data_entry';
        }

        return 'general';
    }

    /**
     * 提取目标网站
     */
    extractTargetWebsite(taskContent, taskType) {
        if (taskType === 'web_automation') {
            const content = taskContent.toLowerCase();
            if (content.includes('商户') || content.includes('bd') || content.includes('小泉居')) {
                return 'https://uat-merchant.aomiapp.com/#/bdlogin';
            }
        }
        return null;
    }

    /**
     * 提取关键实体
     */
    extractKeyEntities(taskContent) {
        const entities = [];

        // 商户名称
        const merchantMatch = taskContent.match(/([^，,。.]*店[^，,。.]*)/g);
        if (merchantMatch) {
            entities.push(...merchantMatch);
        }

        // 商品名称
        const productMatch = taskContent.match(/([^，,。.]*飯[^，,。.]*|[^，,。.]*茶[^，,。.]*|[^，,。.]*湯[^，,。.]*)/g);
        if (productMatch) {
            entities.push(...productMatch);
        }

        // 如果没有找到特定实体，使用任务内容的前50个字符
        if (entities.length === 0) {
            entities.push(taskContent.substring(0, 50));
        }

        return entities;
    }

    /**
     * 提取动作动词
     */
    extractActionVerbs(taskContent) {
        const verbs = [];
        const actionWords = ['下架', '上架', '登录', '点击', '输入', '搜索', '查询', '修改', '删除', '添加'];

        actionWords.forEach(verb => {
            if (taskContent.includes(verb)) {
                verbs.push(verb);
            }
        });

        return verbs.length > 0 ? verbs : ['执行'];
    }

    /**
     * 生成完成标准
     */
    generateCompletionCriteria(taskContent, taskType) {
        if (taskType === 'web_automation') {
            const criteria = [];
            if (taskContent.includes('下架')) {
                criteria.push('商品状态变更为"已下架"');
                criteria.push('页面显示下架成功提示');
            }
            if (taskContent.includes('上架')) {
                criteria.push('商品状态变更为"已上架"');
                criteria.push('页面显示上架成功提示');
            }
            return criteria.length > 0 ? criteria : ['任务执行完成'];
        }
        return ['任务执行完成'];
    }

    /**
     * 估算步骤数
     */
    estimateSteps(taskContent, taskType) {
        if (taskType === 'web_automation') {
            let steps = 3; // 基础步骤：导航、登录、操作
            if (taskContent.includes('商户')) steps += 2; // 选择商户
            if (taskContent.includes('门店')) steps += 2; // 选择门店
            if (taskContent.includes('商品')) steps += 3; // 商品操作
            return Math.min(steps, 15); // 最多15步
        }
        return 5;
    }

    /**
     * 评估复杂度
     */
    assessComplexity(taskContent, taskType) {
        if (taskType === 'web_automation') {
            let complexity = 0;
            if (taskContent.includes('商户')) complexity += 1;
            if (taskContent.includes('门店')) complexity += 1;
            if (taskContent.includes('商品')) complexity += 1;
            if (taskContent.includes('下架') || taskContent.includes('上架')) complexity += 1;

            if (complexity >= 3) return 'high';
            if (complexity >= 2) return 'medium';
            return 'low';
        }
        return 'medium';
    }

    /**
     * 检查登录需求
     */
    checkLoginRequirement(taskContent) {
        const loginKeywords = ['商户', 'bd', '后台', '管理', '登录'];
        return loginKeywords.some(keyword => taskContent.toLowerCase().includes(keyword));
    }

    /**
     * 检查用户输入需求
     */
    checkUserInputRequirement(taskContent) {
        // 通常自动化任务不需要用户输入，除非明确提到
        return taskContent.includes('输入') && taskContent.includes('用户');
    }

    /**
     * 生成战略规划
     */
    generateStrategicPlan(taskContent) {
        const plan = {
            finalGoal: '',
            keyMilestones: [],
            criticalPath: [],
            adaptationPoints: []
        };

        // 分析最终目标
        if (taskContent.includes('下架')) {
            plan.finalGoal = '成功下架指定商品';
            plan.keyMilestones = ['登录系统', '选择商户', '进入门店', '找到商品', '执行下架'];
            plan.criticalPath = ['导航到登录页', '商户搜索和选择', '门店管理', '商品管理', '下架操作'];
            plan.adaptationPoints = ['登录状态检查', '商户选择验证', '门店进入确认', '商品查找结果'];
        } else if (taskContent.includes('上架')) {
            plan.finalGoal = '成功上架指定商品';
            plan.keyMilestones = ['登录系统', '选择商户', '进入门店', '找到商品', '执行上架'];
            plan.criticalPath = ['导航到登录页', '商户搜索和选择', '门店管理', '商品管理', '上架操作'];
            plan.adaptationPoints = ['登录状态检查', '商户选择验证', '门店进入确认', '商品查找结果'];
        } else {
            plan.finalGoal = '完成指定的自动化任务';
            plan.keyMilestones = ['分析任务', '制定计划', '执行操作', '验证结果'];
            plan.criticalPath = ['任务理解', '策略制定', '逐步执行', '结果确认'];
            plan.adaptationPoints = ['任务复杂度评估', '执行策略调整'];
        }

        return plan;
    }

    /**
     * AI驱动的完成度验证
     * @param {string} aiResponse - AI的响应内容
     * @param {Object} originalTask - 原始任务信息
     * @param {Array} executionHistory - 执行历史
     * @returns {Object} 验证结果
     */
    async validateCompletion(aiResponse, originalTask, executionHistory = []) {
        try {
            const validationPrompt = this.buildValidationPrompt(aiResponse, originalTask, executionHistory);
            
            const response = await axios.post(`${this.baseUrl}/chat/completions`, {
                model: this.model,
                messages: [
                    {
                        role: 'system',
                        content: '你是一个专业的RPA任务验证专家。请客观分析任务是否真正完成，不要被表面的回复误导。'
                    },
                    {
                        role: 'user',
                        content: validationPrompt
                    }
                ],
                temperature: 0.1,
                max_tokens: 800
            }, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                }
            });

            const validationResponse = response.data.choices[0].message.content;
            const validation = this.parseValidationResult(validationResponse);
            
            logger.info('🔍 AI完成度验证结果:', validation);
            return validation;

        } catch (error) {
            logger.error('❌ AI完成度验证失败:', error.message);
            return this.getFallbackValidation(aiResponse);
        }
    }

    /**
     * 构建验证提示词
     */
    buildValidationPrompt(aiResponse, originalTask, executionHistory) {
        return `
请验证RPA任务是否真正完成：

原始任务：${originalTask.content}
任务目标：${originalTask.title}

AI最新回复：${aiResponse}

执行历史摘要：
${executionHistory.slice(-5).map((step, index) => 
    `步骤${index + 1}: ${step.action} - ${step.result}`
).join('\n')}

请返回JSON格式的验证结果：
{
    "isCompleted": true/false,
    "confidence": 0.0-1.0,
    "reasoning": "判断理由",
    "evidence": ["支持证据1", "支持证据2"],
    "missingSteps": ["缺失步骤1", "缺失步骤2"],
    "nextAction": "建议的下一步行动",
    "requiresUserIntervention": true/false,
    "interventionType": "需要的用户干预类型(login/input/confirmation/none)"
}

验证标准：
1. 任务是否达到了预期目标
2. 是否有实际的操作执行
3. 是否只是在等待或提示，而没有真正完成
4. 是否需要用户干预才能继续

注意：不要被"任务完成"等表面词汇误导，要看实际执行结果。
`;
    }

    /**
     * 解析验证结果
     */
    parseValidationResult(validationResponse) {
        try {
            const jsonMatch = validationResponse.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                return JSON.parse(jsonMatch[0]);
            }
            return JSON.parse(validationResponse);
        } catch (error) {
            logger.warn('⚠️ 验证结果解析失败，使用保守验证');
            return this.getFallbackValidation(validationResponse);
        }
    }

    /**
     * 获取回退验证结果
     */
    getFallbackValidation(aiResponse) {
        // 保守策略：如果解析失败，假设任务未完成
        return {
            isCompleted: false,
            confidence: 0.3,
            reasoning: '无法准确验证任务完成状态',
            evidence: [],
            missingSteps: ['需要进一步验证'],
            nextAction: '继续执行任务',
            requiresUserIntervention: false,
            interventionType: 'none'
        };
    }

    /**
     * 检测用户干预需求
     * @param {string} aiResponse - AI响应
     * @param {Object} context - 上下文
     * @returns {Object} 干预检测结果
     */
    async detectUserIntervention(aiResponse, context = {}) {
        try {
            const detectionPrompt = `
分析AI回复是否需要用户干预：

AI回复：${aiResponse}
上下文：${JSON.stringify(context)}

返回JSON格式：
{
    "requiresIntervention": true/false,
    "interventionType": "login/input/confirmation/manual_action/none",
    "urgency": "low/medium/high",
    "description": "需要用户做什么",
    "suggestedAction": "建议的用户操作",
    "canWait": true/false,
    "maxWaitTime": 等待时间(秒)
}
`;

            const response = await axios.post(`${this.baseUrl}/chat/completions`, {
                model: this.model,
                messages: [
                    {
                        role: 'system',
                        content: '你是用户干预检测专家，能准确识别何时需要用户介入。'
                    },
                    {
                        role: 'user',
                        content: detectionPrompt
                    }
                ],
                temperature: 0.1,
                max_tokens: 500
            }, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                }
            });

            const detectionResponse = response.data.choices[0].message.content;
            const detection = this.parseValidationResult(detectionResponse);
            
            logger.info('🚨 用户干预检测结果:', detection);
            return detection;

        } catch (error) {
            logger.error('❌ 用户干预检测失败:', error.message);
            return {
                requiresIntervention: false,
                interventionType: 'none',
                urgency: 'low',
                description: '检测失败',
                suggestedAction: '继续执行',
                canWait: true,
                maxWaitTime: 60
            };
        }
    }
}

module.exports = { AITaskAnalyzer };
