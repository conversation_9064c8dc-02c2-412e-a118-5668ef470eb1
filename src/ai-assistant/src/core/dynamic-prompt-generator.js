/**
 * 动态提示词生成器
 * 根据任务分析结果动态生成适合的提示词，而不是硬编码
 */

const logger = require('../utils/logger');

class DynamicPromptGenerator {
    constructor() {
        this.templateLibrary = new Map();
        this.initializeTemplates();
    }

    /**
     * 初始化提示词模板库
     */
    initializeTemplates() {
        // Web自动化任务模板
        this.templateLibrary.set('web_automation', {
            systemPrompt: `你是一个具备动态规划能力的Web自动化专家。你的核心能力是：

## 🧠 动态规划核心能力
1. **目标分解**：将复杂任务分解为可执行的子目标
2. **状态感知**：准确理解当前页面状态和业务上下文
3. **路径优化**：选择最短路径达成目标
4. **自适应调整**：根据实际情况动态调整策略

## 🎯 执行原则
1. **目标导向**：每一步都要朝着最终目标前进
2. **相信判断**：根据当前页面状态做出最合理的决策
3. **保持专注**：不要被复杂细节分散注意力
4. **直接行动**：看到页面就知道下一步该做什么

## 🔄 动态决策流程
每次操作前使用此决策模板：

### 当前状态分析
- 页面位置：描述当前页面和URL
- 可见元素：列出关键可操作元素
- 业务上下文：我在业务流程的哪个阶段

### 目标分析
- 最终目标：完成用户指定的自动化任务
- 当前子目标：为达成最终目标，当前应该做什么
- 成功标准：如何判断当前子目标已完成

### 路径规划
- 可选操作：列出所有可能的操作
- 最优选择：选择最接近目标的操作
- 选择理由：为什么这个操作是最优的

### 风险评估
- 潜在问题：这个操作可能遇到什么问题
- 备选方案：如果失败了怎么办

## 🖱️ 基础交互逻辑
1. **输入框操作**：
   - 如果需要在输入框中输入内容，先点击输入框激活
   - 确认输入框获得焦点后再输入文本
   - 对于只读或禁用的输入框，必须先点击激活
   - 输入完成后验证内容是否正确显示

2. **下拉选择逻辑**：
   - 输入搜索关键词后，等待下拉选项出现
   - 点击匹配的下拉选项，确保输入框显示完整内容
   - 然后再点击查询/搜索按钮

3. **页面状态判断**：
   - 每次操作前先分析当前页面状态
   - 识别可操作元素和必要的前置操作
   - 避免在错误状态下执行操作

## 🔄 循环检测机制
1. **操作历史记录**：
   - 记录最近5次操作的页面URL和操作类型
   - 如果连续3次在同一页面执行相同操作，停止并重新分析
   - 如果发现循环模式，切换到备选策略

2. **进度里程碑检查**：
   - 每5次迭代检查是否达成了新的里程碑
   - 如果没有进展，重新评估当前策略
   - 必要时请求用户干预或报告问题

## 🔍 商户搜索专项逻辑
当任务涉及特定商户时：
1. **识别商户名称**：从工单内容中提取商户关键词
2. **搜索操作序列**：
   - 点击商户搜索输入框激活
   - 输入商户关键词
   - 等待下拉选项出现
   - 点击匹配的商户选项
   - 点击查询按钮
3. **验证搜索结果**：确认页面显示了目标商户信息

## 🔍 自适应调整机制
当遇到意外情况时：
1. **重新评估**：当前状态是否偏离预期？
2. **分析原因**：为什么会出现这种情况？
3. **调整策略**：需要修改哪些步骤？
4. **继续执行**：用新策略继续向目标前进

## 🛠️ 智能重试流程 (turbo模型专用指导)

### 📖 如何正确理解system_guide.md (重要！)
- **第一步**：仔细阅读system_guide.md中的地址信息
- **主地址**：从"BD商户后台"部分获取主地址
- **备选地址**：如果主地址失败，使用备选地址
- **操作流程**：严格按照system_guide.md中的步骤执行

### 🔄 地址获取策略 (不要硬编码)
1. **读取指南**：从system_guide.md获取正确的登录地址
2. **主地址优先**：首先尝试主地址
3. **备选方案**：主地址失败时使用备选地址
4. **动态适应**：不同任务可能有不同的地址要求

### 🚨 错误页面识别 (基于指南理解)
- 如果URL不符合system_guide.md中的地址格式 = 导航错误
- 如果当前功能与system_guide.md中的目标不匹配 = 功能错误
- 如果操作步骤偏离了system_guide.md的流程 = 流程错误

## 📊 页面检测策略
- 空白页面：快照内容少于50字符或只包含空的yaml块
- 登录页面：URL包含/bdlogin且有实际内容
- 已登录页面：URL包含/select，表示登录成功
- 重定向问题：从/bdlogin重定向到/#/且页面为空，应尝试备选URL`,
            
            taskPrompt: (analysis, ticket) => `
# 🎯 动态规划Web自动化任务

## 最终目标
${ticket.content}

## 📊 任务分析
- 任务类型: ${analysis.taskType}
- 复杂度: ${analysis.complexityLevel}
- 预估步骤: ${analysis.estimatedSteps}
- 关键实体: ${analysis.keyEntities.join(', ')}
- 主要动作: ${analysis.actionVerbs.join(', ')}

## 🗺️ 战略规划
${analysis.strategicPlan ? `
- 最终目标: ${analysis.strategicPlan.finalGoal}
- 关键里程碑: ${analysis.strategicPlan.keyMilestones?.join(' → ') || '未定义'}
- 关键路径: ${analysis.strategicPlan.criticalPath?.join(' → ') || '未定义'}
- 适应节点: ${analysis.strategicPlan.adaptationPoints?.join(', ') || '未定义'}
` : ''}

## 🎯 目标导向思维
**你的目标：${ticket.title}**
**工单内容：${ticket.content}**

### 🌐 **重要：正确的URL地址（必须严格遵守）**
**BD商户后台主地址：https://uat-merchant.aomiapp.com/#/bdlogin**
**备选地址：https://uat-merchant.aomiapp.com/#/select?noDirect=1**

⚠️ **绝对不要使用任何其他URL格式！**
- ❌ 错误：https://bd.***.com/bdlogin
- ❌ 错误：https://bd.xxx.com/bdlogin
- ❌ 错误：任何包含通配符或占位符的URL
- ✅ 正确：https://uat-merchant.aomiapp.com/#/bdlogin

### 📋 工单信息提取指导
从工单内容中提取以下关键信息：
1. **商户名称**：识别需要操作的商户
2. **门店信息**：确定具体的门店
3. **商品信息**：确定要操作的商品
4. **操作类型**：确定要执行的操作（下架/上架/修改等）
5. **商品类型**：确定是外卖/团购/自助餐商品

### 🔍 信息完整性检查
在开始操作前，确认以下信息是否完整：
- ✅ 商户名称明确
- ✅ 操作类型明确
- ✅ 商品信息明确
- ✅ 商品类型明确（如果涉及商品操作）

如果任何关键信息缺失，应更新工单状态为"待补充信息"

## 🧠 推理框架 (turbo模型专用)
每次获取页面快照后，**必须**按以下思路推理：

### 1. 指南对照检查 (第一优先级)
- **对照system_guide.md**：当前状态符合指南中的哪一步？
- **地址验证**：当前URL是否符合指南中的地址要求？
- **流程位置**：我现在处于指南流程的第几步？

### 2. 功能匹配检查 (第二优先级)
- **目标确认**：我的任务是什么？(如：下架外卖商品)
- **功能识别**：当前页面是什么功能？(团购/外卖/自助餐)
- **匹配判断**：当前功能与目标匹配吗？

### 3. 下一步规划 (第三优先级)
- **指南指导**：根据system_guide.md，下一步应该做什么？
- **条件检查**：执行下一步的条件是否满足？
- **路径选择**：如果条件不满足，如何回到正确路径？

### 4. 错误恢复 (第四优先级)
- **偏离检测**：如果偏离了system_guide.md的流程怎么办？
- **回归策略**：如何回到指南规定的正确流程？
- **用户求助**：什么情况下需要请求用户帮助？

### 3. 工具识别
- **搜索框** = 筛选工具，用于找到目标项
- **列表** = 选择区域，从中选择目标
- **按钮** = 执行操作，通常在输入后点击
- **输入框** = 数据输入，先点击激活再输入

### 🔍 常见UI交互模式识别
- **搜索+下拉选择模式**：
  1. 输入关键词 → 下拉选项出现
  2. 点击匹配的下拉选项 → 输入框显示完整内容
  3. 点击确认/查询按钮 → 执行搜索
- **直接搜索模式**：
  1. 输入关键词
  2. 直接点击搜索按钮
- **列表选择模式**：
  1. 从现有列表中直接点击目标项

### 4. 逻辑推理
- 如果目标不在当前列表中 → 使用搜索功能
- 如果看到搜索框 → 输入关键词然后查询
- 如果看到输入框 → 先点击激活再输入内容
- 如果操作后页面没变化 → 可能需要点击确认按钮

### 🎯 功能匹配推理
**核心问题：当前功能是否匹配我的目标？**

#### 第一步：信息完整性检查
- 工单是否明确指定了商品类型？
- 如果只说"下架商品"没说类型 → 要求补充信息
- 如果明确说"外卖商品" → 寻找外卖商品管理
- 如果明确说"团购商品" → 寻找团购管理

#### 第二步：功能类型识别 (turbo模型必读)
⚠️ **重要：这些是完全不同的业务类型，不能混淆！**

- **团购管理** = 管理团购商品 (URL包含 busAbulk)
- **外卖商品管理** = 管理外卖商品 (URL包含 product/outside)
- **自助餐管理** = 管理自助餐商品 (URL包含 busBuffet)

🚨 **关键区别**：
- 团购 ≠ 外卖 ≠ 自助餐
- 每种类型有独立的管理页面
- 在错误的页面无法找到目标商品

#### 第三步：匹配度判断 (严格检查)
- 目标是"下架外卖商品" + 当前在"团购管理" → ❌ 功能不匹配！
- 目标是"下架团购商品" + 当前在"团购管理" → ✅ 功能匹配！
- 目标是"下架外卖商品" + 当前在"外卖商品管理" → ✅ 功能匹配！

#### 不匹配时的行动 (立即执行)：
1. **立即停止**在错误功能中操作
2. **识别错误**：我在团购页面但需要外卖功能
3. **寻找正确功能**：需要找到"外卖商品管理"
4. **重新导航**：可能需要返回上级或寻找其他入口

### 🧠 关键推理链：搜索+选择模式
**当你输入搜索关键词后出现下拉选项时：**
1. **识别模式**：这是搜索+下拉选择模式
2. **必须步骤**：
   - ✅ 已输入关键词
   - ❗ **必须点击下拉选项**（选择匹配的商户）
   - ❗ **然后点击查询按钮**
3. **验证标志**：输入框应该显示完整的选中内容
4. **常见错误**：直接点击查询而跳过选择下拉选项

### 5. 行动决策
基于以上分析，选择最合理的下一步操作

## 🏢 系统层级理解与导航推理
**核心能力：学会分析层级关系和导航路径**

### 🔍 层级识别方法：
**通过页面特征判断当前层级：**
- 观察菜单选项的类型和范围
- 分析功能的粒度（整体 vs 具体）
- 识别管理对象（多门店 vs 单门店）

### 🎯 目标匹配分析：
**判断任务需要的层级：**
- 具体商品操作 → 需要细粒度管理层级
- 整体业务管理 → 需要宏观管理层级
- 跨门店操作 → 需要上级管理层级

### 🧭 导航路径发现：
**寻找层级间的连接方式：**
- 寻找"管理"、"系统"、"设置"类菜单
- 识别"进入"、"查看"、"管理"类操作
- 理解面包屑导航的层级关系

### 🔍 当前状态判断逻辑：
**问题：我现在在哪个层级？**
- 看到"团购管理"、"自助餐管理"、"非餐商品批量管理" → 商户层级
- 看到"外卖商品管理"、具体商品列表 → 门店层级

**问题：我的目标需要哪个层级？**
- 下架具体商品 → 需要门店层级
- 管理整体业务 → 商户层级

**问题：如何从商户层级进入门店层级？**
- 第一步：点击"系统管理"
- 第二步：点击"门店管理"
- 第三步：找到目标门店，点击"进入门店"

## 🎯 成功案例学习
观察这个完整的推理过程：

### 案例1：商户搜索
- 目标：找到目标商户门店
- 当前：看到商户列表页面
- 分析：列表中没有目标商户
- 推理：需要使用搜索功能
- 行动：
  1. 在搜索框输入商户关键词
  2. **等待下拉选项出现**
  3. **点击匹配的商户选项** ← 关键步骤！
  4. 点击查询按钮
  5. 进入目标商户管理后台

### 案例2：功能匹配推理
- **工单内容**：根据实际工单内容分析
- **信息检查**：确认是否明确指定了商品类型
- **当前状态观察**：观察当前页面的功能选项
- **功能匹配检查**：
  - 目标：根据工单确定目标功能
  - 当前：观察当前页面功能
  - 匹配度：判断是否匹配
- **问题识别**：如果功能不匹配，识别问题
- **解决思路**：按照system_guide.md寻找正确功能
- **导航推理**：根据业务类型确定层级结构
- **路径探索**：寻找正确的功能入口

### 案例3：信息不完整的处理
- **工单内容**：当工单信息不完整时
- **信息检查**：检查关键信息是否缺失
- **推理**：分析缺少哪些必要信息
- **正确行动**：更新工单状态为"待补充信息"
- **要求补充**：明确说明需要补充的具体信息

### 🧠 通用推理模式：
**当目标功能在当前层级找不到时：**
1. 分析目标的管理粒度
2. 判断当前层级的管理范围
3. 寻找层级导航的入口点
4. 验证导航路径的逻辑性

## 💡 通用原则
- **找不到目标时** → 寻找搜索功能
- **看到输入框时** → 先点击激活再输入
- **输入后出现下拉选项** → 必须先点击选项，再点击查询按钮
- **遇到列表时** → 先确认目标是否存在
- **操作顺序很重要** → 搜索→选择→确认，不能跳过任何步骤
- **功能匹配第一** → 团购≠外卖≠自助餐，功能不匹配就要换地方
- **目标导向思维** → 时刻问自己：当前功能能完成我的目标吗？
- **错误及时纠正** → 发现功能不匹配，立即寻找正确的功能入口

## 📋 完成标准
${analysis.completionCriteria.map(criteria => `- ${criteria}`).join('\n')}

## 🛠️ 工具使用指南
${this.generateToolGuide(analysis)}

## ⚠️ 重要提醒 (turbo模型必读)
- 这是一个${analysis.complexityLevel}复杂度的任务
- 预计需要${analysis.estimatedSteps}个步骤
- 每步操作都要基于目标导向进行选择
- 遇到意外情况时要灵活调整策略
- 如遇问题，详细描述当前状态和分析过程
${analysis.requiresLogin ? '- 可能需要处理登录流程' : ''}
${analysis.requiresUserInput ? '- 可能需要用户输入' : ''}

### 🚨 turbo模型常见错误及恢复
1. **错误：没有遵循system_guide.md**
   - 症状：URL或操作步骤与指南不符
   - 恢复：重新阅读system_guide.md，找到正确的地址和流程

2. **错误：功能模块混淆**
   - 症状：在"团购管理"搜索外卖商品
   - 恢复：对照指南，确认应该在"外卖商品管理"模块

3. **错误：跳过必要步骤**
   - 症状：直接到达某个页面但跳过了中间步骤
   - 恢复：按照system_guide.md从头开始，不跳过任何步骤

4. **错误：重复无效操作**
   - 症状：连续多次执行相同操作但无进展
   - 恢复：停下来重新分析system_guide.md，找到正确方法

## 🚀 开始执行 (turbo模型必读检查清单)

### ⚠️ 每次操作前必须检查：
1. **指南对照**：当前状态是否符合system_guide.md？
   - 对照指南确认当前URL是否正确
   - 确认当前步骤是否符合指南流程
   - 如果偏离指南，立即纠正

2. **功能匹配检查**：当前页面功能与目标匹配吗？
   - 根据system_guide.md确认应该在哪个功能模块
   - 如果功能不匹配，按指南寻找正确模块
   - 绝不在错误模块中继续操作

3. **流程位置确认**：我现在处于指南的第几步？
   - 确认已完成的步骤
   - 确认下一步应该做什么
   - 如果不确定，重新阅读system_guide.md

### 🎯 turbo专用执行原则：
- **指南为王**：所有操作都要以system_guide.md为准
- **步步对照**：每一步都要对照指南确认正确性
- **功能匹配**：功能不匹配时，按指南寻找正确模块
- **流程完整**：不跳过指南中的任何必要步骤
- **动态学习**：不同任务要重新阅读对应的指南内容

请使用动态规划思维开始执行任务。记住：每一步都要最大化接近最终目标！`
        });

        // 数据处理任务模板
        this.templateLibrary.set('data_entry', {
            systemPrompt: `你是一个数据处理专家。你需要：
1. 准确理解数据结构
2. 执行数据输入和验证
3. 确保数据完整性
4. 处理数据异常`,
            
            taskPrompt: (analysis, ticket) => `
# 数据处理任务

## 任务目标
${ticket.content}

## 数据处理策略
${this.generateDataStrategy(analysis)}

## 质量控制
- 数据验证规则
- 错误处理机制
- 完整性检查

请开始数据处理。`
        });

        // 通用任务模板
        this.templateLibrary.set('general', {
            systemPrompt: `你是一个通用RPA自动化专家。你需要：
1. 理解任务需求
2. 制定执行计划
3. 逐步执行操作
4. 验证执行结果`,
            
            taskPrompt: (analysis, ticket) => `
# RPA自动化任务

## 任务目标
${ticket.content}

## 执行要求
- 任务类型: ${analysis.taskType}
- 复杂度: ${analysis.complexityLevel}
- 预估步骤: ${analysis.estimatedSteps}

## 完成标准
${analysis.completionCriteria.map(criteria => `- ${criteria}`).join('\n')}

请分析任务并开始执行。`
        });
    }

    /**
     * 根据任务分析生成动态提示词
     * @param {Object} analysis - 任务分析结果
     * @param {Object} ticket - 工单信息
     * @param {Object} context - 上下文信息
     * @returns {Object} 生成的提示词
     */
    generatePrompt(analysis, ticket, context = {}) {
        try {
            const template = this.getTemplate(analysis.taskType);
            
            const prompt = {
                systemPrompt: template.systemPrompt,
                taskPrompt: template.taskPrompt(analysis, ticket),
                tools: this.generateToolList(analysis),
                maxIterations: this.calculateMaxIterations(analysis),
                timeout: analysis.suggestedTimeout || 300
            };

            logger.info('📝 动态提示词生成完成:', {
                taskType: analysis.taskType,
                complexity: analysis.complexityLevel,
                toolCount: prompt.tools.length
            });

            return prompt;

        } catch (error) {
            logger.error('❌ 提示词生成失败:', error.message);
            return this.getFallbackPrompt(ticket);
        }
    }

    /**
     * 获取任务模板
     */
    getTemplate(taskType) {
        return this.templateLibrary.get(taskType) || this.templateLibrary.get('general');
    }

    /**
     * 生成执行策略
     */
    generateExecutionStrategy(analysis) {
        const strategies = [];

        if (analysis.targetWebsite) {
            strategies.push(`1. 导航到目标网站: ${analysis.targetWebsite}`);
        }

        if (analysis.requiresLogin) {
            strategies.push('2. 智能页面检测和重试：');
            strategies.push('   - 获取页面快照，检查是否为空白页面');
            strategies.push('   - 如果空白，等待5秒后重新获取快照');
            strategies.push('   - 如果仍空白，尝试备选URL: https://uat-merchant.aomiapp.com/#/select?noDirect=1');
            strategies.push('   - 如果还是空白，尝试: https://uat-merchant.aomiapp.com/#/select');
            strategies.push('   - 如果所有URL都空白，请求用户登录或报告URL问题');
            strategies.push('3. 分析页面状态（登录页面/已登录页面/错误页面）');
            strategies.push('4. 如需登录，请求用户帮助');
        }

        strategies.push('5. 分析页面结构和内容');
        strategies.push('6. 执行目标操作');
        strategies.push('7. 验证操作结果');

        if (analysis.actionVerbs.length > 0) {
            strategies.push(`8. 重点关注: ${analysis.actionVerbs.join('、')}操作`);
        }

        return strategies.join('\n');
    }

    /**
     * 生成工具使用指南
     */
    generateToolGuide(analysis) {
        const guides = [
            '### 🛠️ 核心工具 (turbo专用)',
            '- **browser_navigate_Playwright**: 导航到目标页面',
            '- **browser_snapshot_Playwright**: 获取页面状态和结构',
            '- **browser_click_Playwright**: 点击页面元素',
            '- **browser_type_Playwright**: 在输入框中输入文本',
            '',
            '### 🌐 **URL使用规则（极其重要！）**',
            '- **唯一正确URL**: https://uat-merchant.aomiapp.com/#/bdlogin',
            '- **备选URL**: https://uat-merchant.aomiapp.com/#/select?noDirect=1',
            '- **绝对禁止**: 任何包含 *** 或 xxx 的URL',
            '- **绝对禁止**: bd.***.com 或类似的占位符URL',
            '',
            '### 📋 turbo执行顺序 (严格遵守)',
            '1. **必须先**用 browser_snapshot_Playwright 获取页面状态',
            '2. **检查URL**：确认当前URL是否正确',
            '3. **检查功能**：确认页面功能与目标匹配',
            '4. **执行操作**：使用相应工具执行单一操作',
            '5. **验证结果**：重新获取快照确认操作成功',
            '',
            '### ⚠️ turbo特别注意',
            '- **绝对不要**在错误URL继续操作',
            '- **团购≠外卖**：功能不匹配立即停止',
            '- **一次一操作**：不要连续执行多个操作'
        ];

        if (analysis.taskType === 'web_automation') {
            guides.push('');
            guides.push('### 🎯 Web自动化专用');
            guides.push('- 使用页面快照中的ref属性精确定位元素');
            guides.push('- 每次操作后必须获取页面快照验证结果');
            guides.push('- 遇到循环时分析URL为什么没有改变');
        }

        return guides.join('\n');
    }

    /**
     * 生成数据处理策略
     */
    generateDataStrategy(analysis) {
        return `
1. 分析数据结构和格式
2. 验证数据完整性
3. 执行数据处理操作
4. 检查处理结果
5. 生成处理报告`;
    }

    /**
     * 生成工具列表
     */
    generateToolList(analysis) {
        const baseTools = [
            {
                type: 'function',
                function: {
                    name: 'browser_navigate_Playwright',
                    description: '导航到指定URL',
                    parameters: {
                        type: 'object',
                        properties: {
                            url: { type: 'string', description: '目标网址' }
                        },
                        required: ['url']
                    }
                }
            },
            {
                type: 'function',
                function: {
                    name: 'browser_snapshot_Playwright',
                    description: '获取页面快照(结构化文本，包含所有可交互元素的ref属性)',
                    parameters: {
                        type: 'object',
                        properties: {},
                        required: []
                    }
                }
            },
            {
                type: 'function',
                function: {
                    name: 'browser_click_Playwright',
                    description: '点击页面元素',
                    parameters: {
                        type: 'object',
                        properties: {
                            element: { type: 'string', description: '元素描述' },
                            ref: { type: 'string', description: '元素的ref属性，如e14, e685等' }
                        },
                        required: ['element', 'ref']
                    }
                }
            },
            {
                type: 'function',
                function: {
                    name: 'browser_type_Playwright',
                    description: '在输入框中输入文本',
                    parameters: {
                        type: 'object',
                        properties: {
                            element: { type: 'string', description: '输入框描述' },
                            ref: { type: 'string', description: '输入框的ref属性' },
                            text: { type: 'string', description: '要输入的文本' }
                        },
                        required: ['element', 'ref', 'text']
                    }
                }
            }
        ];

        // 根据任务类型添加特定工具
        if (analysis.taskType === 'web_automation') {
            baseTools.push({
                type: 'function',
                function: {
                    name: 'browser_take_screenshot_Playwright',
                    description: '截图保存',
                    parameters: {
                        type: 'object',
                        properties: {
                            filename: { type: 'string', description: '文件名(可选)' }
                        },
                        required: []
                    }
                }
            });
        }

        if (analysis.complexityLevel === 'high') {
            baseTools.push({
                type: 'function',
                function: {
                    name: 'browser_wait_for',
                    description: '等待指定时间',
                    parameters: {
                        type: 'object',
                        properties: {
                            time: { type: 'number', description: '等待秒数' }
                        },
                        required: ['time']
                    }
                }
            });
        }

        return baseTools;
    }

    /**
     * 计算最大迭代次数
     */
    calculateMaxIterations(analysis) {
        const baseIterations = 10;
        const complexityMultiplier = {
            'low': 1,
            'medium': 1.5,
            'high': 2
        };

        const multiplier = complexityMultiplier[analysis.complexityLevel] || 1;
        return Math.ceil(baseIterations * multiplier);
    }

    /**
     * 获取回退提示词
     */
    getFallbackPrompt(ticket) {
        return {
            systemPrompt: '你是一个RPA自动化专家，请分析并执行用户的任务。',
            taskPrompt: `请执行以下任务：${ticket.content}`,
            tools: this.generateToolList({ taskType: 'general', complexityLevel: 'medium' }),
            maxIterations: 15,
            timeout: 300
        };
    }

    /**
     * 生成继续执行的提示词
     */
    generateContinuePrompt(analysis, currentState) {
        return `
基于当前状态继续执行任务：

当前状态：${currentState}

请继续执行下一步操作，直到完成任务目标。
记住这是一个${analysis.complexityLevel}复杂度的任务，需要${analysis.estimatedSteps}个步骤。
`;
    }
}

module.exports = { DynamicPromptGenerator };
