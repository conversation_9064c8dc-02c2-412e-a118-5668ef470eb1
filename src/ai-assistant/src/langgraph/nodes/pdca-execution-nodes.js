/**
 * PDCA执行节点实现
 * 将现有的PlannerAgent、ExecutorAgent、CheckerAgent逻辑迁移到LangGraph节点
 */

const { NodeResult } = require('../state/workflow-state');
// const { IntentValidator } = require('../../pdca/intent-library'); // 暂时注释掉，模块不存在
const logger = require('../../utils/logger');

class PDCAExecutionNodes {
    constructor(dependencies = {}) {
        this.llmClient = dependencies.llmClient;
        this.mcpClient = dependencies.mcpClient;
        this.stateManager = dependencies.stateManager;
        this.ticketManager = dependencies.ticketManager;
        
        // 执行计数器
        this.planningId = 0;
        this.executionId = 0;
        this.checkingId = 0;
    }

    /**
     * 规划节点 - PDCA的Plan阶段
     */
    async planningNode(state) {
        try {
            this.planningId++;
            logger.info(`🧠 PDCA规划阶段 - 循环 ${state.cycleCount + 1}/${state.maxCycles}`);
            
            // 检查挂起状态
            const suspendCheck = await this.checkSuspendStatus(state);
            if (suspendCheck.shouldSuspend) {
                return new NodeResult(true, suspendCheck, 'suspend_node');
            }
            
            state.updateCurrentNode('planning_node');
            state.currentPhase = 'planning';
            state.updatePDCAPhase('plan');
            state.incrementCycle();
            
            // 检查循环次数限制
            const terminationCheck = state.shouldTerminate();
            if (terminationCheck.terminate) {
                logger.warn(`规划阶段终止: ${terminationCheck.reason}`);
                return this.handleTermination(state, terminationCheck.reason);
            }
            
            // 构建规划提示词
            const prompt = this.buildPlanningPrompt(state);
            
            // 调用LLM进行规划
            const llmResponse = await this.llmClient.chat(prompt);
            
            // 解析LLM响应
            const plan = this.parsePlanningResponse(llmResponse);
            
            // 验证计划
            this.validatePlan(plan);
            
            // 保存计划到状态
            state.checkpointData.currentPlan = plan;
            
            // 保存检查点
            await this.stateManager.saveCheckpoint(
                state.workflowId,
                state.createCheckpoint(),
                'planning_node'
            );
            
            logger.info(`✅ 规划完成: ${plan.intent}`, {
                planningId: this.planningId,
                confidence: plan.confidence,
                reasoning: plan.reasoning
            });
            
            return new NodeResult(true, plan, 'execution_node');
            
        } catch (error) {
            logger.error('规划节点失败:', error);
            state.addError(error, 'planning_node');
            return new NodeResult(false, { error: error.message }, 'error_recovery_node');
        }
    }

    /**
     * 执行节点 - PDCA的Do阶段
     */
    async executionNode(state) {
        try {
            this.executionId++;
            const startTime = Date.now();
            
            logger.info('🔧 PDCA执行阶段');
            
            // 检查挂起状态
            const suspendCheck = await this.checkSuspendStatus(state);
            if (suspendCheck.shouldSuspend) {
                return new NodeResult(true, suspendCheck, 'suspend_node');
            }
            
            state.updateCurrentNode('execution_node');
            state.currentPhase = 'executing';
            state.updatePDCAPhase('do');
            
            // 获取当前计划
            const plan = state.checkpointData.currentPlan;
            if (!plan) {
                throw new Error('没有找到执行计划');
            }
            
            logger.info(`🔧 ExecutorAgent开始执行意图: ${plan.intent}`, {
                executionId: this.executionId,
                parameters: plan.parameters
            });
            
            // 验证意图
            try {
                IntentValidator.validateIntent(plan.intent, plan.parameters);
            } catch (error) {
                const failureEvidence = this.createFailureEvidence(plan, null, error, startTime);
                return new NodeResult(true, failureEvidence, 'validation_node');
            }
            
            // 捕获执行前状态
            const beforeState = await this.captureState('before_execution');
            
            // 执行意图对应的原子操作
            const executionResult = await this.executeIntent(plan, state);
            
            // 捕获执行后状态
            const afterState = await this.captureState('after_execution');
            
            // 创建执行证据
            const evidence = this.createSuccessEvidence(
                plan, 
                beforeState, 
                afterState, 
                executionResult, 
                startTime
            );
            
            // 更新状态
            state.checkpointData.currentEvidence = evidence;
            state.updateStats(evidence.success, Date.now() - startTime);
            
            logger.info(`✅ 执行完成: ${plan.intent}`, {
                executionId: this.executionId,
                success: evidence.success,
                duration: Date.now() - startTime
            });
            
            return new NodeResult(true, evidence, 'validation_node');
            
        } catch (error) {
            logger.error('执行节点失败:', error);
            state.addError(error, 'execution_node');
            
            // 创建失败证据
            const plan = state.checkpointData.currentPlan || {};
            const failureEvidence = this.createFailureEvidence(plan, null, error, Date.now());
            
            return new NodeResult(true, failureEvidence, 'validation_node');
        }
    }

    /**
     * 验证节点 - PDCA的Check阶段
     */
    async validationNode(state) {
        try {
            this.checkingId++;
            logger.info('🔍 PDCA验证阶段');
            
            state.updateCurrentNode('validation_node');
            state.currentPhase = 'validating';
            state.updatePDCAPhase('check');
            
            // 获取计划和证据
            const plan = state.checkpointData.currentPlan;
            const evidence = state.checkpointData.currentEvidence;
            
            if (!plan || !evidence) {
                throw new Error('没有找到计划或执行证据');
            }
            
            logger.info(`🔍 CheckerAgent开始检查`, {
                checkingId: this.checkingId,
                intent: plan.intent,
                executionSuccess: evidence.success
            });
            
            // 首先进行基础检查
            const basicCheck = this.performBasicCheck(plan, evidence);
            
            // 如果基础检查失败，直接返回
            if (basicCheck.status === 'FAILURE') {
                state.checkpointData.currentValidation = basicCheck;
                return new NodeResult(true, basicCheck, 'decision_node');
            }
            
            // 进行深度检查（使用LLM）
            const deepCheck = await this.performDeepCheck(plan, evidence, state);
            
            // 合并检查结果
            const finalResult = this.combineCheckResults(basicCheck, deepCheck);
            
            // 保存验证结果
            state.checkpointData.currentValidation = finalResult;
            
            logger.info(`✅ 检查完成: ${finalResult.status}`, {
                checkingId: this.checkingId,
                confidence: finalResult.confidence,
                nextAction: finalResult.nextAction
            });
            
            return new NodeResult(true, finalResult, 'decision_node');
            
        } catch (error) {
            logger.error('验证节点失败:', error);
            state.addError(error, 'validation_node');
            
            // 返回保守的验证结果
            const conservativeResult = {
                status: 'NEEDS_VERIFICATION',
                reason: `验证失败: ${error.message}`,
                confidence: 0.3,
                nextAction: 'CONTINUE',
                suggestions: '验证器出错，建议继续执行并观察'
            };
            
            return new NodeResult(true, conservativeResult, 'decision_node');
        }
    }

    /**
     * 决策节点 - 决定下一步行动
     */
    async decisionNode(state) {
        try {
            logger.info('⚡ PDCA决策阶段');
            
            state.updateCurrentNode('decision_node');
            
            const validation = state.checkpointData.currentValidation;
            
            if (!validation) {
                return new NodeResult(true, { decision: 'error', error: '没有验证结果' });
            }
            
            // 根据验证结果决定下一步
            let decision = 'continue'; // 默认继续循环
            
            switch (validation.nextAction) {
                case 'TASK_COMPLETED':
                    decision = 'complete';
                    break;
                    
                case 'USER_INTERVENTION_REQUIRED':
                    decision = 'intervention';
                    state.requestUserIntervention(
                        validation.reason,
                        validation.requiredInfo || []
                    );
                    break;
                    
                case 'CRITICAL_ERROR':
                    decision = 'error';
                    break;
                    
                case 'CONTINUE':
                default:
                    decision = 'continue';
                    break;
            }
            
            logger.info(`决策结果: ${decision}`, {
                validationStatus: validation.status,
                nextAction: validation.nextAction,
                confidence: validation.confidence
            });
            
            return new NodeResult(true, { 
                decision: decision,
                validation: validation
            });
            
        } catch (error) {
            logger.error('决策节点失败:', error);
            return new NodeResult(true, { 
                decision: 'error', 
                error: error.message 
            });
        }
    }

    /**
     * 行动节点 - PDCA的Act阶段
     */
    async actionNode(state) {
        try {
            logger.info('⚡ PDCA行动阶段');
            
            state.updateCurrentNode('action_node');
            state.updatePDCAPhase('act');
            
            // 更新状态，准备下一轮循环
            this.updateStateForNextCycle(state);
            
            // 压缩历史记录
            state.compressHistory();
            
            // 保存检查点
            await this.stateManager.saveCheckpoint(
                state.workflowId,
                state.createCheckpoint(),
                'action_node'
            );
            
            logger.info(`准备下一轮PDCA循环 (${state.cycleCount}/${state.maxCycles})`);
            
            return new NodeResult(true, { 
                cycleCompleted: true,
                nextCycle: state.cycleCount + 1
            }, 'planning_node');
            
        } catch (error) {
            logger.error('行动节点失败:', error);
            state.addError(error, 'action_node');
            return new NodeResult(false, { error: error.message }, 'error_recovery_node');
        }
    }

    // ===== 辅助方法 =====

    /**
     * 检查挂起状态
     */
    async checkSuspendStatus(state) {
        try {
            const ticket = await this.ticketManager.getTicket(state.ticketId);
            
            if (ticket && ticket.status === '已挂起') {
                logger.info(`工单已挂起: ${state.ticketId}`);
                return { shouldSuspend: true, reason: '工单已挂起' };
            }
            
            if (state.shouldSuspend) {
                return { shouldSuspend: true, reason: '内部请求挂起' };
            }
            
            return { shouldSuspend: false };
            
        } catch (error) {
            logger.error('检查挂起状态失败:', error);
            return { shouldSuspend: false };
        }
    }

    /**
     * 构建规划提示词
     */
    buildPlanningPrompt(state) {
        const context = {
            task_goal: state.taskGoal,
            cycle: state.cycleCount,
            execution_history: state.executionContext.evidenceHistory || [],
            current_state: state.executionContext.browserState,
            errors: state.errorHistory.slice(-3) // 最近的3个错误
        };

        return `# RPA任务规划器

## 当前任务目标
${context.task_goal}

## 当前状态
- 执行周期: ${context.cycle}
- 浏览器状态: ${JSON.stringify(context.current_state, null, 2)}

## 执行历史
${context.execution_history.slice(-3).map((item, index) => 
    `${index + 1}. ${item.intent || '未知操作'} - ${item.success ? '成功' : '失败'}`
).join('\n')}

## 最近错误
${context.errors.map(error => `- ${error.error}`).join('\n')}

请分析当前状态并规划下一步操作。返回JSON格式：
{
  "intent": "操作意图",
  "parameters": {},
  "reasoning": "规划理由",
  "confidence": 0.8
}

可用的操作意图：
- NAVIGATE: 导航到页面
- CLICK: 点击元素
- TYPE: 输入文本
- WAIT: 等待
- SCREENSHOT: 截图
- TASK_COMPLETED: 任务完成
- USER_INTERVENTION_REQUIRED: 需要用户干预`;
    }

    /**
     * 解析规划响应
     */
    parsePlanningResponse(response) {
        try {
            // 清理响应内容
            let cleanResponse = response.trim();
            
            // 移除markdown标记
            if (cleanResponse.startsWith('```json')) {
                cleanResponse = cleanResponse.replace(/```json\n?/, '').replace(/\n?```$/, '');
            }
            
            const plan = JSON.parse(cleanResponse);
            
            // 设置默认值
            return {
                intent: plan.intent || 'WAIT',
                parameters: plan.parameters || {},
                reasoning: plan.reasoning || '无具体原因',
                confidence: plan.confidence || 0.5
            };
            
        } catch (error) {
            logger.error('解析规划响应失败:', error);
            
            // 返回默认计划
            return {
                intent: 'WAIT',
                parameters: { duration: 3000 },
                reasoning: '解析失败，等待观察',
                confidence: 0.3
            };
        }
    }

    /**
     * 验证计划
     */
    validatePlan(plan) {
        const validIntents = [
            'NAVIGATE', 'CLICK', 'TYPE', 'WAIT', 'SCREENSHOT',
            'TASK_COMPLETED', 'USER_INTERVENTION_REQUIRED'
        ];
        
        if (!validIntents.includes(plan.intent)) {
            throw new Error(`无效的操作意图: ${plan.intent}`);
        }
        
        if (plan.confidence < 0 || plan.confidence > 1) {
            plan.confidence = 0.5;
        }
    }

    /**
     * 执行意图
     */
    async executeIntent(plan, state) {
        switch (plan.intent) {
            case 'NAVIGATE':
                return await this.executeNavigate(plan.parameters);
                
            case 'CLICK':
                return await this.executeClick(plan.parameters);
                
            case 'TYPE':
                return await this.executeType(plan.parameters);
                
            case 'WAIT':
                return await this.executeWait(plan.parameters);
                
            case 'SCREENSHOT':
                return await this.executeScreenshot(plan.parameters);
                
            case 'TASK_COMPLETED':
                return { success: true, completed: true };
                
            case 'USER_INTERVENTION_REQUIRED':
                return { success: true, needsIntervention: true };
                
            default:
                throw new Error(`不支持的操作意图: ${plan.intent}`);
        }
    }

    /**
     * 执行导航操作
     */
    async executeNavigate(parameters) {
        const url = parameters.url || 'https://uat-merchant.aomiapp.com/#/bdlogin';
        
        await this.mcpClient.callTool('browser_navigate', { url });
        await this.sleep(3000); // 等待页面加载
        
        return { success: true, action: 'navigate', url: url };
    }

    /**
     * 执行点击操作
     */
    async executeClick(parameters) {
        const { element, ref } = parameters;
        
        if (!ref) {
            throw new Error('点击操作缺少ref参数');
        }
        
        await this.mcpClient.callTool('browser_click', { element, ref });
        await this.sleep(1000); // 等待响应
        
        return { success: true, action: 'click', element, ref };
    }

    /**
     * 执行输入操作
     */
    async executeType(parameters) {
        const { element, ref, text } = parameters;
        
        if (!ref || !text) {
            throw new Error('输入操作缺少必要参数');
        }
        
        await this.mcpClient.callTool('browser_type', { 
            element, 
            ref, 
            text, 
            submit: false 
        });
        
        return { success: true, action: 'type', element, text: text.length > 50 ? text.substring(0, 50) + '...' : text };
    }

    /**
     * 执行等待操作
     */
    async executeWait(parameters) {
        const duration = parameters.duration || 3000;
        
        await this.sleep(duration);
        
        return { success: true, action: 'wait', duration };
    }

    /**
     * 执行截图操作
     */
    async executeScreenshot(parameters) {
        const screenshot = await this.mcpClient.callTool('browser_screenshot', {});
        
        return { 
            success: true, 
            action: 'screenshot', 
            screenshot: screenshot?.substring(0, 100) + '...' // 截取部分用于日志
        };
    }

    /**
     * 捕获状态
     */
    async captureState(phase) {
        try {
            const snapshot = await this.mcpClient.callTool('browser_snapshot', {});
            
            return {
                phase: phase,
                timestamp: new Date().toISOString(),
                snapshot: snapshot?.substring(0, 1000) || '', // 截取部分
                url: 'current_page' // 实际实现中应该获取真实URL
            };
            
        } catch (error) {
            logger.error('捕获状态失败:', error);
            return {
                phase: phase,
                timestamp: new Date().toISOString(),
                error: error.message
            };
        }
    }

    /**
     * 创建成功证据
     */
    createSuccessEvidence(plan, beforeState, afterState, executionResult, startTime) {
        return {
            success: true,
            plan: plan,
            beforeState: beforeState,
            afterState: afterState,
            executionResult: executionResult,
            duration: Date.now() - startTime,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 创建失败证据
     */
    createFailureEvidence(plan, beforeState, error, startTime) {
        return {
            success: false,
            plan: plan,
            beforeState: beforeState,
            error: {
                message: error.message || error,
                stack: error.stack
            },
            duration: Date.now() - startTime,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 执行基础检查
     */
    performBasicCheck(plan, evidence) {
        // 基础成功性检查
        if (!evidence.success) {
            return {
                status: 'FAILURE',
                reason: '执行失败',
                confidence: 0.9,
                nextAction: 'CONTINUE',
                suggestions: '重试执行'
            };
        }
        
        // 特殊意图检查
        if (plan.intent === 'TASK_COMPLETED') {
            return {
                status: 'SUCCESS',
                reason: '任务完成',
                confidence: 0.95,
                nextAction: 'TASK_COMPLETED'
            };
        }
        
        if (plan.intent === 'USER_INTERVENTION_REQUIRED') {
            return {
                status: 'SUCCESS',
                reason: '需要用户干预',
                confidence: 0.9,
                nextAction: 'USER_INTERVENTION_REQUIRED'
            };
        }
        
        return {
            status: 'SUCCESS',
            reason: '基础检查通过',
            confidence: 0.7,
            nextAction: 'CONTINUE'
        };
    }

    /**
     * 执行深度检查
     */
    async performDeepCheck(plan, evidence, state) {
        try {
            // 构建检查提示词
            const prompt = this.buildCheckingPrompt(plan, evidence, state);
            
            // 调用LLM进行深度分析
            const response = await this.llmClient.chat(prompt);
            
            // 解析检查结果
            return this.parseCheckingResponse(response);
            
        } catch (error) {
            logger.error('深度检查失败:', error);
            
            return {
                status: 'NEEDS_VERIFICATION',
                reason: '深度检查失败',
                confidence: 0.4,
                nextAction: 'CONTINUE'
            };
        }
    }

    /**
     * 构建检查提示词
     */
    buildCheckingPrompt(plan, evidence, state) {
        return `# RPA执行结果检查

## 执行计划
- 意图: ${plan.intent}
- 参数: ${JSON.stringify(plan.parameters)}
- 理由: ${plan.reasoning}

## 执行结果
- 成功: ${evidence.success}
- 操作: ${evidence.executionResult?.action || '未知'}
- 耗时: ${evidence.duration}ms

## 任务目标
${state.taskGoal}

## 当前周期
${state.cycleCount}/${state.maxCycles}

请分析执行结果是否符合预期，返回JSON格式：
{
  "status": "SUCCESS|FAILURE|NEEDS_VERIFICATION",
  "reason": "检查理由",
  "confidence": 0.8,
  "nextAction": "CONTINUE|TASK_COMPLETED|USER_INTERVENTION_REQUIRED|CRITICAL_ERROR",
  "suggestions": "改进建议"
}`;
    }

    /**
     * 解析检查响应
     */
    parseCheckingResponse(response) {
        try {
            let cleanResponse = response.trim();
            
            if (cleanResponse.startsWith('```json')) {
                cleanResponse = cleanResponse.replace(/```json\n?/, '').replace(/\n?```$/, '');
            }
            
            const result = JSON.parse(cleanResponse);
            
            return {
                status: result.status || 'NEEDS_VERIFICATION',
                reason: result.reason || '无具体原因',
                confidence: result.confidence || 0.5,
                nextAction: result.nextAction || 'CONTINUE',
                suggestions: result.suggestions || '无建议'
            };
            
        } catch (error) {
            logger.error('解析检查响应失败:', error);
            
            return {
                status: 'NEEDS_VERIFICATION',
                reason: '解析失败',
                confidence: 0.3,
                nextAction: 'CONTINUE'
            };
        }
    }

    /**
     * 合并检查结果
     */
    combineCheckResults(basicCheck, deepCheck) {
        // 如果基础检查失败，以基础检查为准
        if (basicCheck.status === 'FAILURE') {
            return basicCheck;
        }
        
        // 如果深度检查置信度更高，以深度检查为准
        if (deepCheck.confidence > basicCheck.confidence) {
            return deepCheck;
        }
        
        // 否则以基础检查为准，但合并信息
        return {
            ...basicCheck,
            deepCheckReason: deepCheck.reason,
            suggestions: deepCheck.suggestions
        };
    }

    /**
     * 处理终止情况
     */
    handleTermination(state, reason) {
        switch (reason) {
            case 'MAX_CYCLES_REACHED':
                return new NodeResult(true, { 
                    decision: 'error', 
                    error: '达到最大循环次数' 
                }, 'error_recovery_node');
                
            case 'MAX_CONSECUTIVE_FAILURES':
                return new NodeResult(true, { 
                    decision: 'error', 
                    error: '连续失败次数过多' 
                }, 'error_recovery_node');
                
            case 'SUSPENDED':
                return new NodeResult(true, { 
                    decision: 'suspend' 
                }, 'suspend_node');
                
            case 'USER_INTERVENTION_REQUIRED':
                return new NodeResult(true, { 
                    decision: 'intervention' 
                }, 'user_intervention_node');
                
            default:
                return new NodeResult(true, { 
                    decision: 'error', 
                    error: '未知终止原因' 
                }, 'error_recovery_node');
        }
    }

    /**
     * 更新状态为下一轮循环
     */
    updateStateForNextCycle(state) {
        // 重置连续失败计数（如果本轮成功）
        const validation = state.checkpointData.currentValidation;
        if (validation && validation.status === 'SUCCESS') {
            state.resetConsecutiveFailures();
        }
        
        // 添加执行记录到历史
        const executionRecord = {
            cycle: state.cycleCount,
            plan: state.checkpointData.currentPlan,
            evidence: state.checkpointData.currentEvidence,
            validation: validation,
            timestamp: new Date().toISOString()
        };
        
        state.executionContext.evidenceHistory.push(executionRecord);
        
        // 清除当前周期的临时数据
        delete state.checkpointData.currentPlan;
        delete state.checkpointData.currentEvidence;
        delete state.checkpointData.currentValidation;
    }

    /**
     * 睡眠函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

module.exports = PDCAExecutionNodes;