/**
 * LangGraph RPA工作流定义
 * 定义完整的工单处理工作流图
 */

const { StateGraph } = require('@langchain/langgraph');
const { WorkflowState, NodeResult } = require('../state/workflow-state');
const WorkflowStateManager = require('../state/workflow-state-manager');
const logger = require('../../utils/logger');

class RPAWorkflow {
    constructor(dependencies = {}) {
        this.stateManager = new WorkflowStateManager();
        this.dependencies = {
            mcpClient: dependencies.mcpClient,
            llmClient: dependencies.llmClient,
            ticketManager: dependencies.ticketManager,
            ...dependencies
        };
        
        // 节点名称常量
        this.NODES = {
            START: 'start_node',
            RECOVERY_CHECK: 'recovery_check_node',
            QUEUE_MANAGEMENT: 'queue_management_node',
            PLANNING: 'planning_node',
            EXECUTION: 'execution_node',
            VALIDATION: 'validation_node',
            DECISION: 'decision_node',
            ACTION: 'action_node',
            COMPLETION: 'completion_node',
            USER_INTERVENTION: 'user_intervention_node',
            SUSPEND: 'suspend_node',
            ERROR_RECOVERY: 'error_recovery_node',
            CLEANUP: 'cleanup_node'
        };
        
        this.workflow = null;
        this.isInitialized = false;
    }

    /**
     * 初始化工作流
     */
    async initialize() {
        try {
            logger.info('🚀 初始化LangGraph RPA工作流...');
            
            // 创建状态图
            this.workflow = new StateGraph({
                stateSchema: WorkflowState
            });

            // 添加所有节点
            this.addNodes();
            
            // 定义节点间的连接
            this.addEdges();
            
            // 设置入口点
            this.workflow.setEntryPoint(this.NODES.START);
            
            // 编译工作流
            this.compiledWorkflow = this.workflow.compile();
            
            this.isInitialized = true;
            logger.info('✅ LangGraph RPA工作流初始化完成');
            
        } catch (error) {
            logger.error('❌ LangGraph工作流初始化失败:', error);
            throw error;
        }
    }

    /**
     * 添加所有节点
     */
    addNodes() {
        // 启动节点
        this.workflow.addNode(this.NODES.START, this.startNode.bind(this));
        
        // 恢复检查节点
        this.workflow.addNode(this.NODES.RECOVERY_CHECK, this.recoveryCheckNode.bind(this));
        
        // 队列管理节点
        this.workflow.addNode(this.NODES.QUEUE_MANAGEMENT, this.queueManagementNode.bind(this));
        
        // PDCA核心节点
        this.workflow.addNode(this.NODES.PLANNING, this.planningNode.bind(this));
        this.workflow.addNode(this.NODES.EXECUTION, this.executionNode.bind(this));
        this.workflow.addNode(this.NODES.VALIDATION, this.validationNode.bind(this));
        this.workflow.addNode(this.NODES.DECISION, this.decisionNode.bind(this));
        this.workflow.addNode(this.NODES.ACTION, this.actionNode.bind(this));
        
        // 控制节点
        this.workflow.addNode(this.NODES.COMPLETION, this.completionNode.bind(this));
        this.workflow.addNode(this.NODES.USER_INTERVENTION, this.userInterventionNode.bind(this));
        this.workflow.addNode(this.NODES.SUSPEND, this.suspendNode.bind(this));
        this.workflow.addNode(this.NODES.ERROR_RECOVERY, this.errorRecoveryNode.bind(this));
        this.workflow.addNode(this.NODES.CLEANUP, this.cleanupNode.bind(this));
    }

    /**
     * 定义节点间的连接
     */
    addEdges() {
        // 主要流程路径
        this.workflow.addEdge(this.NODES.START, this.NODES.RECOVERY_CHECK);
        this.workflow.addEdge(this.NODES.RECOVERY_CHECK, this.NODES.QUEUE_MANAGEMENT);
        this.workflow.addEdge(this.NODES.QUEUE_MANAGEMENT, this.NODES.PLANNING);
        
        // PDCA循环
        this.workflow.addEdge(this.NODES.PLANNING, this.NODES.EXECUTION);
        this.workflow.addEdge(this.NODES.EXECUTION, this.NODES.VALIDATION);
        this.workflow.addEdge(this.NODES.VALIDATION, this.NODES.DECISION);
        
        // 决策分支（通过条件边实现）
        this.workflow.addConditionalEdges(
            this.NODES.DECISION,
            this.routeDecision.bind(this),
            {
                'continue': this.NODES.ACTION,
                'complete': this.NODES.COMPLETION,
                'intervention': this.NODES.USER_INTERVENTION,
                'error': this.NODES.ERROR_RECOVERY,
                'suspend': this.NODES.SUSPEND
            }
        );
        
        // 行动节点回到规划（循环）
        this.workflow.addEdge(this.NODES.ACTION, this.NODES.PLANNING);
        
        // 特殊情况处理
        this.workflow.addEdge(this.NODES.USER_INTERVENTION, this.NODES.SUSPEND);
        this.workflow.addEdge(this.NODES.ERROR_RECOVERY, this.NODES.PLANNING);
        this.workflow.addEdge(this.NODES.SUSPEND, this.NODES.CLEANUP);
        this.workflow.addEdge(this.NODES.COMPLETION, this.NODES.CLEANUP);
        
        // 挂起检查（每个节点都检查）
        this.addSuspendChecks();
    }

    /**
     * 添加挂起检查
     */
    addSuspendChecks() {
        const nodesThatCheckSuspend = [
            this.NODES.PLANNING,
            this.NODES.EXECUTION,
            this.NODES.VALIDATION,
            this.NODES.ACTION
        ];

        nodesThatCheckSuspend.forEach(nodeName => {
            // 在每个节点执行前检查挂起状态
            // 这个检查会在节点内部实现
        });
    }

    /**
     * 启动节点 - 工作流入口点
     */
    async startNode(state) {
        try {
            logger.info(`🎯 启动工作流处理工单: ${state.ticketId}`);
            
            // 创建工作流状态记录
            if (!state.workflowId) {
                state.workflowId = await this.stateManager.createWorkflowState(
                    state.ticketId,
                    state.executionContext
                );
            }
            
            state.updateCurrentNode(this.NODES.START);
            
            // 保存检查点
            await this.stateManager.saveCheckpoint(
                state.workflowId,
                state.createCheckpoint(),
                this.NODES.START
            );
            
            return new NodeResult(true, null, this.NODES.RECOVERY_CHECK);
            
        } catch (error) {
            logger.error('启动节点失败:', error);
            return new NodeResult(false, null, this.NODES.ERROR_RECOVERY);
        }
    }

    /**
     * 恢复检查节点 - AI助手重启时的状态恢复
     */
    async recoveryCheckNode(state) {
        try {
            logger.info('🔄 执行恢复检查...');
            
            state.updateCurrentNode(this.NODES.RECOVERY_CHECK);
            
            // 检查是否有需要恢复的状态
            const existingState = await this.stateManager.getWorkflowStateByTicketId(state.ticketId);
            
            if (existingState && existingState.status === 'suspended') {
                logger.info(`发现挂起的工作流，恢复执行: ${existingState.workflow_id}`);
                
                // 恢复工作流状态
                await this.stateManager.resumeWorkflow(existingState.workflow_id);
                
                // 合并状态数据
                Object.assign(state, WorkflowState.fromSerializable(existingState.checkpoint_data));
                state.workflowId = existingState.workflow_id;
            }
            
            return new NodeResult(true, null, this.NODES.QUEUE_MANAGEMENT);
            
        } catch (error) {
            logger.error('恢复检查失败:', error);
            return new NodeResult(false, null, this.NODES.ERROR_RECOVERY);
        }
    }

    /**
     * 队列管理节点 - 处理工单队列逻辑
     */
    async queueManagementNode(state) {
        try {
            logger.info('📋 执行队列管理...');
            
            state.updateCurrentNode(this.NODES.QUEUE_MANAGEMENT);
            state.currentPhase = 'queue';
            
            // 检查工单状态
            const ticket = await this.dependencies.ticketManager.getTicket(state.ticketId);
            if (!ticket) {
                throw new Error(`工单不存在: ${state.ticketId}`);
            }
            
            state.ticket = ticket;
            state.originalContent = ticket.content;
            state.taskGoal = `${ticket.title}: ${ticket.content}`;
            
            // 保存检查点
            await this.stateManager.saveCheckpoint(
                state.workflowId,
                state.createCheckpoint(),
                this.NODES.QUEUE_MANAGEMENT
            );
            
            return new NodeResult(true, null, this.NODES.PLANNING);
            
        } catch (error) {
            logger.error('队列管理失败:', error);
            state.addError(error, this.NODES.QUEUE_MANAGEMENT);
            return new NodeResult(false, null, this.NODES.ERROR_RECOVERY);
        }
    }

    /**
     * 规划节点 - PDCA的Plan阶段
     */
    async planningNode(state) {
        try {
            logger.info(`🧠 PDCA规划阶段 - 循环 ${state.cycleCount + 1}`);
            
            // 检查挂起状态
            const suspendCheck = await this.checkSuspendStatus(state);
            if (suspendCheck.shouldSuspend) {
                return new NodeResult(true, suspendCheck, this.NODES.SUSPEND);
            }
            
            state.updateCurrentNode(this.NODES.PLANNING);
            state.currentPhase = 'planning';
            state.updatePDCAPhase('plan');
            state.incrementCycle();
            
            // 检查循环次数限制
            const terminationCheck = state.shouldTerminate();
            if (terminationCheck.terminate) {
                logger.warn(`规划阶段终止: ${terminationCheck.reason}`);
                return this.handleTermination(state, terminationCheck.reason);
            }
            
            // 调用规划逻辑（这里会调用原有的PlannerAgent逻辑）
            const planResult = await this.executePlanningLogic(state);
            
            state.checkpointData.lastPlan = planResult;
            
            // 保存检查点
            await this.stateManager.saveCheckpoint(
                state.workflowId,
                state.createCheckpoint(),
                this.NODES.PLANNING
            );
            
            return new NodeResult(true, planResult, this.NODES.EXECUTION);
            
        } catch (error) {
            logger.error('规划节点失败:', error);
            state.addError(error, this.NODES.PLANNING);
            return new NodeResult(false, null, this.NODES.ERROR_RECOVERY);
        }
    }

    /**
     * 执行节点 - PDCA的Do阶段
     */
    async executionNode(state) {
        try {
            logger.info('🔧 PDCA执行阶段');
            
            // 检查挂起状态
            const suspendCheck = await this.checkSuspendStatus(state);
            if (suspendCheck.shouldSuspend) {
                return new NodeResult(true, suspendCheck, this.NODES.SUSPEND);
            }
            
            state.updateCurrentNode(this.NODES.EXECUTION);
            state.currentPhase = 'executing';
            state.updatePDCAPhase('do');
            
            // 执行计划（调用ExecutorAgent逻辑）
            const executionResult = await this.executeActionPlan(state);
            
            state.checkpointData.lastExecution = executionResult;
            state.updateStats(executionResult.success, executionResult.duration);
            
            return new NodeResult(true, executionResult, this.NODES.VALIDATION);
            
        } catch (error) {
            logger.error('执行节点失败:', error);
            state.addError(error, this.NODES.EXECUTION);
            return new NodeResult(false, null, this.NODES.ERROR_RECOVERY);
        }
    }

    /**
     * 验证节点 - PDCA的Check阶段
     */
    async validationNode(state) {
        try {
            logger.info('🔍 PDCA验证阶段');
            
            state.updateCurrentNode(this.NODES.VALIDATION);
            state.currentPhase = 'validating';
            state.updatePDCAPhase('check');
            
            // 验证执行结果（调用CheckerAgent逻辑）
            const validationResult = await this.validateExecution(state);
            
            state.checkpointData.lastValidation = validationResult;
            
            return new NodeResult(true, validationResult, this.NODES.DECISION);
            
        } catch (error) {
            logger.error('验证节点失败:', error);
            state.addError(error, this.NODES.VALIDATION);
            return new NodeResult(false, null, this.NODES.ERROR_RECOVERY);
        }
    }

    /**
     * 决策节点 - 决定下一步行动
     */
    async decisionNode(state) {
        try {
            logger.info('⚡ PDCA决策阶段');
            
            state.updateCurrentNode(this.NODES.DECISION);
            
            const lastValidation = state.checkpointData.lastValidation;
            
            // 根据验证结果决定下一步
            if (lastValidation.status === 'TASK_COMPLETED') {
                return new NodeResult(true, { decision: 'complete' });
            }
            
            if (lastValidation.status === 'USER_INTERVENTION_REQUIRED') {
                return new NodeResult(true, { 
                    decision: 'intervention',
                    reason: lastValidation.reason 
                });
            }
            
            if (lastValidation.status === 'CRITICAL_ERROR') {
                return new NodeResult(true, { 
                    decision: 'error',
                    error: lastValidation.error 
                });
            }
            
            // 默认继续循环
            return new NodeResult(true, { decision: 'continue' });
            
        } catch (error) {
            logger.error('决策节点失败:', error);
            return new NodeResult(true, { decision: 'error', error: error });
        }
    }

    /**
     * 行动节点 - PDCA的Act阶段
     */
    async actionNode(state) {
        try {
            logger.info('⚡ PDCA行动阶段');
            
            state.updateCurrentNode(this.NODES.ACTION);
            state.updatePDCAPhase('act');
            
            // 更新状态，准备下一轮循环
            state.compressHistory();
            
            // 保存检查点
            await this.stateManager.saveCheckpoint(
                state.workflowId,
                state.createCheckpoint(),
                this.NODES.ACTION
            );
            
            return new NodeResult(true, null, this.NODES.PLANNING);
            
        } catch (error) {
            logger.error('行动节点失败:', error);
            state.addError(error, this.NODES.ACTION);
            return new NodeResult(false, null, this.NODES.ERROR_RECOVERY);
        }
    }

    /**
     * 完成节点 - 任务完成处理
     */
    async completionNode(state) {
        try {
            logger.info('🎉 任务完成处理');
            
            state.updateCurrentNode(this.NODES.COMPLETION);
            state.currentPhase = 'completed';
            
            // 生成最终报告
            const finalReport = this.generateFinalReport(state);
            state.setFinalResult({ success: true }, finalReport);
            
            // 完成工作流
            await this.stateManager.completeWorkflow(state.workflowId, state.finalResult);
            
            return new NodeResult(true, state.finalResult, this.NODES.CLEANUP);
            
        } catch (error) {
            logger.error('完成节点失败:', error);
            return new NodeResult(false, null, this.NODES.ERROR_RECOVERY);
        }
    }

    /**
     * 用户干预节点 - 处理需要人工干预的情况
     */
    async userInterventionNode(state) {
        try {
            logger.info('🤝 用户干预处理');
            
            state.updateCurrentNode(this.NODES.USER_INTERVENTION);
            
            // 更新工单状态，请求用户干预
            await this.requestUserIntervention(state);
            
            return new NodeResult(true, null, this.NODES.SUSPEND);
            
        } catch (error) {
            logger.error('用户干预节点失败:', error);
            return new NodeResult(false, null, this.NODES.ERROR_RECOVERY);
        }
    }

    /**
     * 挂起节点 - 处理工作流挂起
     */
    async suspendNode(state) {
        try {
            logger.info('⏸️ 工作流挂起处理');
            
            state.updateCurrentNode(this.NODES.SUSPEND);
            
            // 挂起工作流
            await this.stateManager.suspendWorkflow(state.workflowId, '用户操作或需要人工干预');
            
            return new NodeResult(true, null, this.NODES.CLEANUP);
            
        } catch (error) {
            logger.error('挂起节点失败:', error);
            return new NodeResult(false, null, this.NODES.CLEANUP);
        }
    }

    /**
     * 错误恢复节点 - 处理错误和重试
     */
    async errorRecoveryNode(state) {
        try {
            logger.info('🔧 错误恢复处理');
            
            state.updateCurrentNode(this.NODES.ERROR_RECOVERY);
            
            // 错误恢复逻辑
            const recovery = await this.handleErrorRecovery(state);
            
            if (recovery.shouldRetry) {
                logger.info('重试执行...');
                return new NodeResult(true, recovery, this.NODES.PLANNING);
            } else {
                logger.error('无法恢复，标记为失败');
                await this.stateManager.failWorkflow(state.workflowId, recovery.error);
                return new NodeResult(false, recovery, this.NODES.CLEANUP);
            }
            
        } catch (error) {
            logger.error('错误恢复失败:', error);
            return new NodeResult(false, null, this.NODES.CLEANUP);
        }
    }

    /**
     * 清理节点 - 清理资源和完成工作流
     */
    async cleanupNode(state) {
        try {
            logger.info('🧹 资源清理');
            
            state.updateCurrentNode(this.NODES.CLEANUP);
            
            // 清理浏览器资源
            await this.cleanupResources(state);
            
            // 工作流结束
            logger.info(`✅ 工作流完成: ${state.workflowId}`);
            
            return new NodeResult(true, { completed: true });
            
        } catch (error) {
            logger.error('清理节点失败:', error);
            return new NodeResult(false, null);
        }
    }

    /**
     * 决策路由函数
     */
    routeDecision(state) {
        const result = state.checkpointData.lastValidation || {};
        const decision = result.decision || 'continue';
        
        logger.info(`决策路由: ${decision}`);
        return decision;
    }

    /**
     * 检查挂起状态
     */
    async checkSuspendStatus(state) {
        try {
            // 检查工单状态是否为已挂起
            const ticket = await this.dependencies.ticketManager.getTicket(state.ticketId);
            
            if (ticket && ticket.status === '已挂起') {
                logger.info(`工单已挂起: ${state.ticketId}`);
                return { shouldSuspend: true, reason: '工单已挂起' };
            }
            
            // 检查内部挂起标志
            if (state.shouldSuspend) {
                return { shouldSuspend: true, reason: '内部请求挂起' };
            }
            
            return { shouldSuspend: false };
            
        } catch (error) {
            logger.error('检查挂起状态失败:', error);
            return { shouldSuspend: false };
        }
    }

    /**
     * 处理终止情况
     */
    handleTermination(state, reason) {
        switch (reason) {
            case 'MAX_CYCLES_REACHED':
                return new NodeResult(true, { decision: 'error', error: '达到最大循环次数' });
            case 'MAX_CONSECUTIVE_FAILURES':
                return new NodeResult(true, { decision: 'error', error: '连续失败次数过多' });
            case 'SUSPENDED':
                return new NodeResult(true, { decision: 'suspend' });
            case 'USER_INTERVENTION_REQUIRED':
                return new NodeResult(true, { decision: 'intervention' });
            default:
                return new NodeResult(true, { decision: 'error', error: '未知终止原因' });
        }
    }

    // 占位符方法 - 这些将在后续阶段实现
    async executePlanningLogic(state) { return { intent: 'navigate', confidence: 0.8 }; }
    async executeActionPlan(state) { return { success: true, duration: 1000 }; }
    async validateExecution(state) { return { status: 'SUCCESS' }; }
    async requestUserIntervention(state) { return true; }
    async handleErrorRecovery(state) { return { shouldRetry: false }; }
    async cleanupResources(state) { return true; }
    generateFinalReport(state) { return { summary: '任务完成' }; }

    /**
     * 执行工作流
     */
    async execute(initialState) {
        if (!this.isInitialized) {
            await this.initialize();
        }
        
        try {
            logger.info(`🚀 开始执行LangGraph工作流: ${initialState.ticketId}`);
            
            const result = await this.compiledWorkflow.invoke(initialState);
            
            logger.info(`✅ LangGraph工作流执行完成: ${initialState.ticketId}`);
            return result;
            
        } catch (error) {
            logger.error('LangGraph工作流执行失败:', error);
            throw error;
        }
    }
}

module.exports = RPAWorkflow;