/**
 * 真正的MCP执行器 - 让qwen-plus真正调用Playwright MCP工具
 * 实现真正的工具调用和反馈循环
 */

const axios = require('axios');
const logger = require('../utils/logger');
const { NotificationService } = require('../services/notification-service');
require('dotenv').config();

class RealMCPExecutor {
    constructor(mcpClient) {
        this.mcpClient = mcpClient;
        this.modelClient = this.createModelClient();
        this.notificationService = new NotificationService(logger);
    }

    createModelClient() {
        const self = this; // 保存this引用
        return {
            apiKey: process.env.DASHSCOPE_API_KEY,
            baseUrl: process.env.DASHSCOPE_BASE_URL,
            model: 'qwen-plus',

            async callWithTools(prompt, tools, maxIterations = 15, notificationService = null, currentTicketId = null) {
                let iteration = 0;
                let conversationHistory = [{ role: 'user', content: prompt }];
                let results = [];

                while (iteration < maxIterations) {
                    iteration++;
                    logger.info(`🔄 MCP工具调用迭代 ${iteration}/${maxIterations}`);

                    try {
                        // 调用qwen-plus，提供真正的工具定义
                        const response = await axios.post(`${this.baseUrl}/chat/completions`, {
                            model: this.model,
                            messages: conversationHistory,
                            tools: tools,
                            tool_choice: 'auto',
                            temperature: 0.1,
                            max_tokens: 2000
                        }, {
                            headers: {
                                'Authorization': `Bearer ${this.apiKey}`,
                                'Content-Type': 'application/json'
                            }
                        });

                        const message = response.data.choices[0].message;
                        logger.info('🤖 AI 原始响应:\n', JSON.stringify(message, null, 2));
                        logger.info(`🤖 AI响应内容: ${message.content || '无内容'}`);
                        logger.info(`🔧 工具调用数量: ${message.tool_calls?.length || 0}`);
                        logger.info(`🔄 当前迭代: ${iteration}/${maxIterations}`);
                        conversationHistory.push(message);

                        // 检查是否有工具调用
                        if (message.tool_calls && message.tool_calls.length > 0) {
                            logger.info(`🔧 qwen-turbo请求调用 ${message.tool_calls.length} 个工具`);

                            // 执行每个工具调用
                            for (const toolCall of message.tool_calls) {
                                const toolResult = await self.executeToolCall(toolCall);
                                results.push(toolResult);

                                // 获取页面状态（如果是浏览器操作）
                                let pageState = null;
                                const toolName = toolCall.function.name;
                                if (['browser_navigate_Playwright', 'browser_click_Playwright', 'browser_type_Playwright'].includes(toolName)) {
                                    try {
                                        pageState = await self.mcpClient.getPageSnapshot();
                                        logger.info('📸 获取页面状态用于AI反馈');
                                    } catch (e) {
                                        logger.warn('⚠️ 获取页面状态失败，继续执行');
                                    }
                                }

                                // 将工具执行结果添加到对话历史，包含页面状态和继续提醒
                                conversationHistory.push({
                                    role: 'tool',
                                    tool_call_id: toolCall.id,
                                    content: JSON.stringify({
                                        ...toolResult,
                                        pageState: pageState,
                                        reminder: "请基于当前页面状态继续执行下一步操作，直到任务完全完成。这是一个多步骤任务。"
                                    })
                                });
                            }
                        } else {
                            // 没有工具调用，检查AI是否真的认为任务完成了
                            const content = message.content || '';
                            logger.info(`🤖 AI响应内容: ${content}`);

                            // 检查AI是否明确表示任务完成
                            if (content.includes('任务完成') || content.includes('已完成') || content.includes('完成了')) {
                                logger.info('✅ qwen-turbo确认任务完成');
                                return {
                                    success: true,
                                    finalMessage: content,
                                    results: results,
                                    iterations: iteration
                                };
                            }

                            // 检查是否需要用户登录
                            if (content.includes('请手动登录') || content.includes('用户手动登录') || content.includes('需要登录')) {
                                // 检测到需要用户登录，发送通知
                                logger.info('🔔 检测到需要用户登录，发送通知');

                                if (notificationService && currentTicketId) {
                                    // 发送登录需求通知
                                    await notificationService.sendLoginRequiredNotification(
                                        currentTicketId,
                                        'https://uat-merchant.aomiapp.com/#/bdlogin'
                                    );

                                    // 等待用户登录完成
                                    logger.info('⏳ 等待用户完成登录...');
                                    const loginCompleted = await notificationService.waitForUserIntervention(
                                        currentTicketId,
                                        300000 // 等待5分钟
                                    );

                                    if (loginCompleted) {
                                        logger.info('✅ 用户已完成登录，继续执行任务');
                                        conversationHistory.push({
                                            role: 'user',
                                            content: "用户已完成登录，请继续执行任务的下一步操作。先使用 browser_snapshot_Playwright 获取当前页面状态，然后根据页面内容继续操作。"
                                        });
                                    } else {
                                        logger.warn('⏰ 用户登录等待超时，任务暂停');
                                        await notificationService.sendTaskPausedNotification(
                                            currentTicketId,
                                            '等待用户登录超时'
                                        );
                                        return {
                                            success: false,
                                            error: '等待用户登录超时',
                                            finalMessage: content,
                                            results: results,
                                            iterations: iteration
                                        };
                                    }
                                } else {
                                    logger.warn('⚠️ 通知服务未配置，无法发送登录通知');
                                    // 继续执行，但记录警告
                                    conversationHistory.push({
                                        role: 'user',
                                        content: "请继续执行任务的下一步操作。如果需要，先使用 browser_snapshot_Playwright 获取当前页面状态，然后根据页面内容继续操作。记住这是一个多步骤任务，需要持续操作直到完全完成。"
                                    });
                                }
                            } else {
                                // 强制AI继续工作
                                logger.info('🔄 AI未明确完成任务，强制继续执行');
                                conversationHistory.push({
                                    role: 'user',
                                    content: "请继续执行任务的下一步操作。如果需要，先使用 browser_snapshot_Playwright 获取当前页面状态，然后根据页面内容继续操作。记住这是一个多步骤任务，需要持续操作直到完全完成。"
                                });
                            }
                        }

                    } catch (error) {
                        logger.error(`❌ MCP工具调用迭代 ${iteration} 失败:`, error);
                        return {
                            success: false,
                            error: error.message,
                            results: results,
                            iterations: iteration
                        };
                    }
                }

                logger.warn('⚠️ 达到最大迭代次数，任务可能未完成');
                return {
                    success: false,
                    error: '达到最大迭代次数',
                    results: results,
                    iterations: iteration
                };
            }
        };
    }

    /**
     * 执行真正的工具调用
     */
    async executeToolCall(toolCall) {
        try {
            const { name, arguments: args } = toolCall.function;
            const parsedArgs = JSON.parse(args);

            logger.info(`🛠️ 执行工具: ${name}`);
            logger.info(`📋 参数:`, parsedArgs);

            let result;

            switch (name) {
                case 'browser_navigate_Playwright':
                    result = await this.mcpClient.navigate(parsedArgs.url);
                    break;

                case 'browser_snapshot_Playwright':
                    result = await this.mcpClient.getPageSnapshot();
                    break;

                case 'browser_click_Playwright':
                    result = await this.mcpClient.clickElement(
                        parsedArgs.element,
                        parsedArgs.ref
                    );
                    break;

                case 'browser_type_Playwright':
                    result = await this.mcpClient.typeText(
                        parsedArgs.element,
                        parsedArgs.ref,
                        parsedArgs.text
                    );
                    break;

                case 'browser_take_screenshot_Playwright':
                    result = await this.mcpClient.takeScreenshot(parsedArgs.filename);
                    break;

                case 'browser_wait_for_Playwright':
                    result = await this.mcpClient.wait(parsedArgs.time || 3);
                    break;

                default:
                    throw new Error(`不支持的工具: ${name}`);
            }

            logger.info(`✅ 工具执行成功: ${name}`);
            return {
                success: true,
                tool: name,
                result: result,
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            logger.error(`❌ 工具执行失败: ${toolCall.function.name}`, error);
            return {
                success: false,
                tool: toolCall.function.name,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * 生成真正的Playwright MCP工具定义
     */
    generatePlaywrightTools() {
        return [
            {
                type: 'function',
                function: {
                    name: 'browser_navigate_Playwright',
                    description: '导航到指定URL',
                    parameters: {
                        type: 'object',
                        properties: {
                            url: {
                                type: 'string',
                                description: '目标网址'
                            }
                        },
                        required: ['url']
                    }
                }
            },
            {
                type: 'function',
                function: {
                    name: 'browser_snapshot_Playwright',
                    description: '获取页面快照(结构化文本，包含所有可交互元素的ref属性)',
                    parameters: {
                        type: 'object',
                        properties: {},
                        required: []
                    }
                }
            },
            {
                type: 'function',
                function: {
                    name: 'browser_click_Playwright',
                    description: '点击页面元素',
                    parameters: {
                        type: 'object',
                        properties: {
                            element: {
                                type: 'string',
                                description: '元素描述'
                            },
                            ref: {
                                type: 'string',
                                description: '元素的ref属性，如e14, e685等'
                            }
                        },
                        required: ['element', 'ref']
                    }
                }
            },
            {
                type: 'function',
                function: {
                    name: 'browser_type_Playwright',
                    description: '在输入框中输入文本',
                    parameters: {
                        type: 'object',
                        properties: {
                            element: {
                                type: 'string',
                                description: '输入框描述'
                            },
                            ref: {
                                type: 'string',
                                description: '输入框的ref属性'
                            },
                            text: {
                                type: 'string',
                                description: '要输入的文本'
                            }
                        },
                        required: ['element', 'ref', 'text']
                    }
                }
            },
            {
                type: 'function',
                function: {
                    name: 'browser_take_screenshot_Playwright',
                    description: '截图保存',
                    parameters: {
                        type: 'object',
                        properties: {
                            filename: {
                                type: 'string',
                                description: '文件名(可选)'
                            }
                        },
                        required: []
                    }
                }
            },
            {
                type: 'function',
                function: {
                    name: 'browser_wait_for',
                    description: '等待指定时间',
                    parameters: {
                        type: 'object',
                        properties: {
                            time: {
                                type: 'number',
                                description: '等待秒数'
                            }
                        },
                        required: ['time']
                    }
                }
            }
        ];
    }

    /**
     * 执行真正的RPA任务
     */
    async executeRealRPATask(ticket) {
        try {
            logger.info(`🚀 开始执行真正的MCP架构RPA任务: ${ticket.id}`);

            // 设置当前工单ID，用于通知服务
            this.currentTicketId = ticket.id;

            // 构建真正的任务提示词
            const prompt = this.buildRealTaskPrompt(ticket);
            
            // 生成真正的Playwright工具定义
            const tools = this.generatePlaywrightTools();

            // 执行真正的工具调用循环
            const result = await this.modelClient.callWithTools(prompt, tools, 15, this.notificationService, this.currentTicketId);

            logger.info(`✅ 真正的MCP架构RPA任务完成: ${ticket.id}`);
            
            return {
                success: result.success,
                ticketId: ticket.id,
                finalMessage: result.finalMessage,
                toolResults: result.results,
                iterations: result.iterations,
                error: result.error
            };

        } catch (error) {
            logger.error(`❌ 真正的MCP架构RPA任务失败: ${ticket.id}`, error);
            return {
                success: false,
                ticketId: ticket.id,
                error: error.message
            };
        }
    }

    /**
     * 构建真正的任务提示词
     */
    buildRealTaskPrompt(ticket) {
        // 分析任务类型，确定目标网站
        let targetUrl = 'https://uat-merchant.aomiapp.com/#/bdlogin';
        let taskType = 'general';

        const content = ticket.content || '';

        if (content.includes('下架') || content.includes('商品') || content.includes('小泉居')) {
            taskType = 'product_delisting';
            targetUrl = 'https://uat-merchant.aomiapp.com/#/bdlogin';
        }

        return `# AI-First RPA自动化任务 - 商户后台操作

## 任务目标
${ticket.content}

## 系统指南 (必须严格遵循)
这是一个商户后台操作任务，请按照以下标准流程执行：

### BD商户后台地址
- 主地址: https://uat-merchant.aomiapp.com/#/bdlogin
- 备选地址: https://uat-merchant.aomiapp.com/#/select?noDirect=1

### 登录流程说明
1. **登录页面** (/bdlogin)
   - 如果显示登录表单，请提醒用户手动登录
   - 如果已登录，会自动跳转到选择页面
2. **选择页面** (/select)
   - 登录成功后的正常状态
   - 显示门店选择界面，包含门店输入框和查询按钮

### 下架门店外卖商品标准流程
1. 打开BD商户后台 (${targetUrl})
2. 如需登录，要求用户帮忙登录，登录成功后才继续
3. 在门店输入框输入完整门店id或门店名称关键词筛选门店，点击目标门店，并点击查询进入商户管理后台
4. 如点击查询没有进入商户管理后台，则点击页面上的相应门店名称
5. 在商户管理后台点击左侧菜单的"系统管理"展开菜单，点击"门店管理"
6. 在门店列表点击目标门店的"进入门店"按钮，进入门店管理后台
7. 在门店管理后台点击左侧菜单的"商品管理"展开菜单，点击"外卖商品管理"
8. 在外卖商品列表的搜索框，选择搜索类型(商品名称/商品id)，输入关键词，点击🔍搜索
9. 在搜索列表滚动查找目标商品，找到后点击"下架"，二次确认后即可下架
10. 找到所有该下架的商品并操作下架后，即完成操作

## 执行要求
1. 这是一个多步骤任务，需要持续操作直到完成
2. 每次操作后，根据页面状态决定下一步
3. 使用 browser_snapshot_Playwright 获取页面信息
4. 严格按照系统指南的流程执行
5. 只有在确认任务100%完成后才停止

## 工具使用指南
- 使用 browser_navigate_Playwright 导航到目标页面
- 使用 browser_snapshot_Playwright 获取页面状态
- 使用 browser_click_Playwright 点击按钮和链接
- 使用 browser_type_Playwright 在搜索框输入内容
- 使用页面快照中的ref属性精确定位元素

## 重要提醒
- 不要在第一步后就停止
- 每步操作后都要分析页面状态
- 持续操作直到达成任务目标
- 如遇到登录页面，提醒用户手动登录后继续
- 严格按照系统指南的标准流程执行
- 商品下架操作在外卖商品管理页面进行

## 任务信息
- 工单ID: ${ticket.id}
- 标题: ${ticket.title}
- 内容: ${ticket.content}
- 目标网站: ${targetUrl}

请开始执行，首先导航到商户后台: ${targetUrl}`;
    }
}

module.exports = RealMCPExecutor;
