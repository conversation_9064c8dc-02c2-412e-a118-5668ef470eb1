/**
 * 简化执行器 - 绕过复杂的MCP协议，直接使用Playwright
 * 解决连接层复杂度问题
 */

const { SimpleMCPClient } = require('../playwright-mcp/simple-mcp-client');
const axios = require('axios');
require('dotenv').config();

class SimpleExecutor {
    constructor(logger = console) {
        this.logger = logger;
        this.client = new SimpleMCPClient(logger);
        this.isInitialized = false;
    }

    /**
     * 初始化
     */
    async initialize() {
        try {
            this.logger.info('🚀 初始化简化执行器...');
            
            const success = await this.client.initialize();
            if (success) {
                this.isInitialized = true;
                this.logger.info('✅ 简化执行器初始化完成');
                return true;
            } else {
                throw new Error('浏览器初始化失败');
            }
        } catch (error) {
            this.logger.error('❌ 简化执行器初始化失败:', error.message);
            return false;
        }
    }

    /**
     * 执行RPA任务
     */
    async executeTask(workOrder) {
        try {
            this.logger.info(`🎯 开始执行简化RPA任务: ${workOrder.id}`);
            
            if (!this.isInitialized) {
                await this.initialize();
            }

            // 更新工单状态为处理中
            await this.updateWorkOrderStatus(workOrder.id, '处理中');

            // 使用AI生成任务步骤
            const tasks = await this.generateTasks(workOrder);
            
            if (!tasks || tasks.length === 0) {
                throw new Error('AI未能生成有效的任务步骤');
            }

            this.logger.info(`📋 AI生成了 ${tasks.length} 个任务步骤`);

            // 执行任务步骤
            const results = [];
            for (let i = 0; i < tasks.length; i++) {
                const task = tasks[i];
                this.logger.info(`🔄 执行步骤 ${i + 1}/${tasks.length}: ${task.action}`);
                
                try {
                    const result = await this.executeStep(task);
                    results.push({
                        step: i + 1,
                        task: task,
                        result: result,
                        success: true
                    });
                    
                    this.logger.info(`✅ 步骤 ${i + 1} 执行成功`);
                } catch (stepError) {
                    this.logger.error(`❌ 步骤 ${i + 1} 执行失败:`, stepError.message);
                    results.push({
                        step: i + 1,
                        task: task,
                        error: stepError.message,
                        success: false
                    });
                    
                    // 继续执行下一步，不中断整个流程
                }
            }

            // 生成执行报告
            const report = this.generateReport(workOrder, results);
            
            // 更新工单状态为已完成
            await this.updateWorkOrderStatus(workOrder.id, '已完成', report);
            
            this.logger.info(`✅ 简化RPA任务执行完成: ${workOrder.id}`);
            
            return {
                success: true,
                workOrderId: workOrder.id,
                results: results,
                report: report
            };

        } catch (error) {
            this.logger.error(`❌ 简化RPA任务执行失败:`, error.message);
            
            // 更新工单状态为处理失败
            await this.updateWorkOrderStatus(workOrder.id, '处理失败', {
                error: error.message,
                timestamp: new Date().toISOString()
            });
            
            throw error;
        }
    }

    /**
     * 使用AI生成任务步骤
     */
    async generateTasks(workOrder) {
        try {
            this.logger.info('🤖 使用AI生成任务步骤...');
            
            const prompt = `你是一个RPA自动化专家。请根据以下工单内容，生成具体的浏览器操作步骤。

工单标题: ${workOrder.title}
工单内容: ${workOrder.content}

请生成JSON格式的任务步骤，每个步骤包含：
- action: 操作类型 (navigate, snapshot, type, click, wait, screenshot)
- description: 操作描述
- parameters: 操作参数

示例格式：
[
  {"action": "navigate", "description": "访问百度首页", "parameters": {"url": "https://www.baidu.com"}},
  {"action": "snapshot", "description": "获取页面快照", "parameters": {}},
  {"action": "type", "description": "在搜索框输入关键词", "parameters": {"element": "搜索框", "ref": "input_0", "text": "RPA自动化"}},
  {"action": "click", "description": "点击搜索按钮", "parameters": {"element": "百度一下", "ref": "btn_0"}},
  {"action": "wait", "description": "等待页面加载", "parameters": {"seconds": 3}},
  {"action": "screenshot", "description": "截图保存结果", "parameters": {"filename": "search_result.png"}}
]

请只返回JSON数组，不要包含其他文字。`;

            const response = await axios.post(
                `${process.env.DASHSCOPE_BASE_URL}/chat/completions`,
                {
                    model: process.env.MAIN_MODEL || 'qwen-turbo',
                    messages: [
                        {
                            role: 'user',
                            content: prompt
                        }
                    ],
                    temperature: 0.1,
                    max_tokens: 2000
                },
                {
                    headers: {
                        'Authorization': `Bearer ${process.env.DASHSCOPE_API_KEY}`,
                        'Content-Type': 'application/json'
                    },
                    timeout: 30000
                }
            );

            const aiResponse = response.data.choices[0].message.content;
            this.logger.info('🤖 AI响应:', aiResponse);

            // 解析AI响应
            const tasks = JSON.parse(aiResponse);
            
            this.logger.info(`✅ AI生成了 ${tasks.length} 个任务步骤`);
            return tasks;

        } catch (error) {
            this.logger.error('❌ AI任务生成失败:', error.message);
            
            // 返回默认的测试任务
            return [
                {"action": "navigate", "description": "访问百度首页", "parameters": {"url": "https://www.baidu.com"}},
                {"action": "snapshot", "description": "获取页面快照", "parameters": {}},
                {"action": "screenshot", "description": "截图保存", "parameters": {"filename": "test_result.png"}}
            ];
        }
    }

    /**
     * 执行单个步骤
     */
    async executeStep(task) {
        const { action, parameters } = task;
        
        switch (action) {
            case 'navigate':
                return await this.client.navigate(parameters.url);
            
            case 'snapshot':
                return await this.client.snapshot();
            
            case 'type':
                return await this.client.type(parameters.element, parameters.ref, parameters.text);
            
            case 'click':
                return await this.client.click(parameters.element, parameters.ref);
            
            case 'wait':
                return await this.client.wait(parameters.seconds);
            
            case 'screenshot':
                return await this.client.takeScreenshot(parameters.filename);
            
            default:
                throw new Error(`未知的操作类型: ${action}`);
        }
    }

    /**
     * 生成执行报告
     */
    generateReport(workOrder, results) {
        const successCount = results.filter(r => r.success).length;
        const totalCount = results.length;
        
        return {
            workOrderId: workOrder.id,
            title: workOrder.title,
            executionTime: new Date().toISOString(),
            totalSteps: totalCount,
            successSteps: successCount,
            successRate: `${((successCount / totalCount) * 100).toFixed(1)}%`,
            results: results,
            summary: `执行了 ${totalCount} 个步骤，成功 ${successCount} 个，成功率 ${((successCount / totalCount) * 100).toFixed(1)}%`
        };
    }

    /**
     * 更新工单状态
     */
    async updateWorkOrderStatus(workOrderId, status, result = null) {
        try {
            const updateData = { status };
            if (result) {
                updateData.result = result;
            }
            
            await axios.patch(`http://localhost:3001/api/tickets/${workOrderId}`, updateData);
            this.logger.info(`✅ 工单状态已更新: ${workOrderId} -> ${status}`);
        } catch (error) {
            this.logger.error(`❌ 更新工单状态失败:`, error.message);
        }
    }

    /**
     * 清理资源
     */
    async cleanup() {
        try {
            await this.client.close();
            this.logger.info('✅ 简化执行器资源清理完成');
        } catch (error) {
            this.logger.error('❌ 简化执行器资源清理失败:', error.message);
        }
    }
}

module.exports = { SimpleExecutor };
