/**
 * AI执行控制器 - AI-First RPA架构的核心
 * 使用Playwright MCP实现真正的AI驱动浏览器自动化
 * 无需本地浏览器，通过MCP调用实现所有操作
 */

const fs = require('fs').promises;
const path = require('path');
const { chromium } = require('playwright');
const PlaywrightExecutor = require('../playwright-enhanced/playwright-executor');
const PlaywrightAnalyzer = require('../playwright-enhanced/playwright-analyzer');

class AIExecutionController {
    constructor(logger = console) {
        this.logger = logger;
        this.modelClient = null;
        this.systemGuide = null;

        // 真正的Playwright组件 - 直接浏览器控制
        this.browser = null;
        this.page = null;
        this.playwrightExecutor = null;
        this.playwrightAnalyzer = null;

        // 保留MCP客户端用于快照分析
        this.mcpClient = null;

        this.executionContext = {
            taskDescription: '',
            currentObjective: '',
            completedSteps: [],
            encounteredIssues: [],
            screenshots: []
        };
    }

    /**
     * 初始化AI执行控制器 - 使用真正的Playwright
     */
    async initialize(modelClient) {
        try {
            this.logger.info('🚀 初始化AI执行控制器 (真正的Playwright)...');

            this.modelClient = modelClient;

            // 加载系统指南
            await this.loadSystemGuide();

            // 初始化真正的Playwright浏览器
            await this.initializeRealPlaywright();

            // 初始化MCP客户端（用于快照分析）
            await this.initializeMCPClient();

            this.logger.info('✅ AI执行控制器 (真正的Playwright) 初始化完成');
            return true;
        } catch (error) {
            this.logger.error('❌ AI执行控制器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 加载系统操作指南
     */
    async loadSystemGuide() {
        try {
            const guidePath = path.join(__dirname, '../../../../system_guide.md');
            this.systemGuide = await fs.readFile(guidePath, 'utf-8');
            this.logger.info('✅ 系统指南加载成功');
        } catch (error) {
            this.logger.error('❌ 系统指南加载失败:', error);
            throw error;
        }
    }

    /**
     * 初始化真正的Playwright浏览器
     */
    async initializeRealPlaywright() {
        try {
            this.logger.info('🚀 启动真正的Playwright浏览器...');

            // 启动浏览器
            this.browser = await chromium.launch({
                headless: false,
                slowMo: 1000,
                args: ['--start-maximized']
            });

            // 创建页面
            this.page = await this.browser.newPage();
            await this.page.setViewportSize({ width: 1920, height: 1080 });

            // 创建Playwright增强组件
            this.playwrightExecutor = new PlaywrightExecutor(this.page, this.logger);
            this.playwrightAnalyzer = new PlaywrightAnalyzer(this.page, this.logger);

            this.logger.info('✅ 真正的Playwright浏览器初始化完成');
        } catch (error) {
            this.logger.error('❌ Playwright浏览器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 初始化MCP客户端（用于快照分析）
     */
    async initializeMCPClient() {
        try {
            this.logger.info('🚀 初始化MCP客户端（快照分析）...');

            // 创建MCP客户端实例（不连接外部服务器）
            const PlaywrightMCPClient = require('../playwright-mcp/playwright-mcp-client');
            this.mcpClient = new PlaywrightMCPClient(this.logger);

            // 简单初始化，不连接外部MCP服务器
            this.mcpClient.isInitialized = true;

            this.logger.info('✅ MCP客户端（快照分析）初始化完成');
        } catch (error) {
            this.logger.error('❌ MCP客户端初始化失败:', error);
            throw error;
        }
    }

    /**
     * 清理资源
     */
    async cleanup() {
        try {
            if (this.browser) {
                await this.browser.close();
                this.browser = null;
                this.page = null;
                this.playwrightExecutor = null;
                this.playwrightAnalyzer = null;
                this.logger.info('✅ Playwright浏览器已关闭');
            }
        } catch (error) {
            this.logger.error('❌ 清理资源失败:', error);
        }
    }

    /**
     * 执行任务 - AI-First RPA的核心方法
     */
    async executeTask(task) {
        try {
            this.logger.info(`🎯 开始执行AI-First RPA任务: ${task.description || task.id}`);
            
            // 初始化执行上下文
            this.executionContext = {
                taskDescription: task.description || '未知任务',
                currentObjective: '开始任务执行',
                completedSteps: [],
                encounteredIssues: [],
                screenshots: []
            };

            const startTime = Date.now();
            let stepCount = 0;
            const maxSteps = 20; // 防止无限循环

            // 如果任务有初始操作，先执行
            if (task.initialAction) {
                this.logger.info(`🎬 执行初始操作: ${task.initialAction.action_type}`);
                const initialResult = await this.executeAction(task.initialAction);
                this.executionContext.completedSteps.push({
                    action: task.initialAction,
                    result: initialResult,
                    verification: { isValid: initialResult.success, hasError: !initialResult.success },
                    timestamp: new Date().toISOString()
                });
                stepCount++;

                // 等待页面加载
                await this.page.waitForTimeout(3000);
            }

            let currentState = await this.perceivePage();

            while (!this.isTaskComplete(currentState, task) && stepCount < maxSteps) {
                stepCount++;
                this.logger.info(`🔄 执行步骤 ${stepCount}/${maxSteps}`);

                try {
                    // 直接使用Playwright分析结果，让AI基于结构化数据做决策
                    const nextAction = await this.planNextActionFromPlaywrightData(currentState, this.executionContext);
                    
                    // 执行操作
                    const result = await this.executeAction(nextAction);
                    
                    // 验证结果
                    const verification = await this.verifyActionResult(result, nextAction);
                    
                    // 更新状态和上下文
                    currentState = await this.perceivePage();
                    this.updateContext(result, verification, nextAction);
                    
                    // 错误处理
                    if (verification.hasError) {
                        await this.handleError(verification.error, this.executionContext);
                    }

                    // 短暂等待，让页面稳定
                    await this.page.waitForTimeout(2000);
                    
                } catch (stepError) {
                    this.logger.error(`❌ 步骤 ${stepCount} 执行失败:`, stepError);
                    this.executionContext.encounteredIssues.push({
                        step: stepCount,
                        error: stepError.message,
                        timestamp: new Date().toISOString()
                    });
                    
                    // 如果连续失败，停止执行
                    if (this.executionContext.encounteredIssues.length >= 3) {
                        break;
                    }
                }
            }

            const executionTime = Date.now() - startTime;
            const report = await this.generateExecutionReport(this.executionContext, executionTime);
            
            this.logger.info(`🎉 任务执行完成，耗时: ${executionTime}ms`);
            
            return {
                success: this.executionContext.encounteredIssues.length < 3,
                stepsCompleted: stepCount,
                executionTime,
                report
            };

        } catch (error) {
            this.logger.error('❌ 任务执行失败:', error);
            return {
                success: false,
                error: error.message,
                stepsCompleted: 0,
                executionTime: 0,
                report: {
                    summary: `任务执行失败: ${error.message}`,
                    completedSteps: this.executionContext.completedSteps,
                    issues: this.executionContext.encounteredIssues,
                    screenshots: this.executionContext.screenshots
                }
            };
        }
    }

    /**
     * 感知当前页面状态 - 使用MCP获取页面快照
     */
    async perceivePage() {
        try {
            this.logger.info('👁️ 感知页面状态 (MCP模式)...');

            // 使用MCP获取页面快照 - 这是我成功的关键
            const snapshotResult = await this.mcpClient.getPageSnapshot();

            if (!snapshotResult.success) {
                throw new Error(`页面快照获取失败: ${snapshotResult.error}`);
            }

            // 解析快照数据，提取可操作元素
            const pageData = this.parsePageSnapshot(snapshotResult.snapshot);

            this.logger.info('✅ 页面感知完成');

            return {
                url: snapshotResult.url || 'unknown',
                title: pageData.title || 'unknown',
                screenshot: null, // MCP快照不包含图片数据
                viewport: null,
                timestamp: Date.now(),

                // 从快照解析的元素信息
                buttons: pageData.buttons || [],
                inputs: pageData.inputs || [],
                links: pageData.links || [],
                forms: pageData.forms || [],
                headings: pageData.headings || [],
                images: pageData.images || [],
                pageState: pageData.pageState || {},
                accessibility: pageData.accessibility || {},

                // 原始快照数据
                rawSnapshot: snapshotResult.snapshot
            };
        } catch (error) {
            this.logger.error('❌ 页面感知失败:', error);
            return {
                url: 'unknown',
                title: 'unknown',
                screenshot: null,
                viewport: null,
                timestamp: Date.now(),
                buttons: [],
                inputs: [],
                links: [],
                forms: [],
                headings: [],
                images: [],
                pageState: {},
                accessibility: {},
                rawSnapshot: null
            };
        }
    }

    /**
     * 解析页面快照 - 从MCP快照中提取可操作元素
     */
    parsePageSnapshot(snapshot) {
        try {
            if (!snapshot) {
                return { buttons: [], inputs: [], links: [], forms: [] };
            }

            // 解析快照中的元素信息
            const buttons = [];
            const inputs = [];
            const links = [];
            const forms = [];

            // 这里需要根据实际的快照格式进行解析
            // 基于我的成功经验，快照通常包含元素的ref和描述

            // 示例解析逻辑（需要根据实际快照格式调整）
            if (typeof snapshot === 'string') {
                // 如果快照是字符串格式，解析其中的元素信息
                const lines = snapshot.split('\n');

                lines.forEach(line => {
                    if (line.includes('button') && line.includes('[ref=')) {
                        const refMatch = line.match(/\[ref=([^\]]+)\]/);
                        const textMatch = line.match(/"([^"]+)"/);

                        if (refMatch && textMatch) {
                            buttons.push({
                                ref: refMatch[1],
                                text: textMatch[1],
                                isEnabled: !line.includes('[disabled]'),
                                description: textMatch[1]
                            });
                        }
                    }

                    if (line.includes('textbox') && line.includes('[ref=')) {
                        const refMatch = line.match(/\[ref=([^\]]+)\]/);
                        const placeholderMatch = line.match(/"([^"]+)"/);

                        if (refMatch) {
                            inputs.push({
                                ref: refMatch[1],
                                placeholder: placeholderMatch ? placeholderMatch[1] : '',
                                isEnabled: !line.includes('[disabled]'),
                                type: 'text'
                            });
                        }
                    }

                    if (line.includes('link') && line.includes('[ref=')) {
                        const refMatch = line.match(/\[ref=([^\]]+)\]/);
                        const textMatch = line.match(/"([^"]+)"/);

                        if (refMatch && textMatch) {
                            links.push({
                                ref: refMatch[1],
                                text: textMatch[1],
                                description: textMatch[1]
                            });
                        }
                    }
                });
            }

            return {
                title: this.extractTitleFromSnapshot(snapshot),
                buttons: buttons,
                inputs: inputs,
                links: links,
                forms: forms,
                pageState: {
                    isLoading: false,
                    hasErrors: false,
                    hasModals: snapshot.includes('dialog')
                }
            };
        } catch (error) {
            this.logger.error('❌ 快照解析失败:', error);
            return { buttons: [], inputs: [], links: [], forms: [] };
        }
    }

    /**
     * 从快照中提取页面标题
     */
    extractTitleFromSnapshot(snapshot) {
        try {
            if (typeof snapshot === 'string') {
                const titleMatch = snapshot.match(/heading "([^"]+)"/);
                if (titleMatch) {
                    return titleMatch[1];
                }
            }
            return 'unknown';
        } catch (error) {
            return 'unknown';
        }
    }

    /**
     * 使用AI分析页面状态 - 结合MCP快照结果
     */
    async analyzePageWithAI(pageState) {
        try {
            // 构建丰富的页面信息提示词
            const pageInfo = this.buildPageInfoPrompt(pageState);

            const prompt = `
你是一个专业的RPA自动化助手，正在分析网页状态以决定下一步操作。

当前任务: ${this.executionContext.taskDescription}
当前目标: ${this.executionContext.currentObjective}

${pageInfo}

系统操作指南:
${this.systemGuide}

已完成的步骤:
${this.executionContext.completedSteps.map((step, i) => `${i+1}. ${step.action?.action_type || 'unknown'} - ${step.action?.reasoning || 'no reason'}`).join('\n')}

请基于Playwright分析的详细页面信息，提供下一步操作建议。返回JSON格式:
{
    "summary": "页面状态描述",
    "currentStep": "当前应该执行的步骤",
    "possibleActions": ["可能的操作列表"],
    "recommendedElement": {
        "type": "button|input|link",
        "description": "推荐操作的元素描述",
        "selector": "元素选择器或定位方式"
    },
    "confidence": 0.8
}`;

            const response = await this.modelClient.callVisionModel(prompt, pageState.screenshot);

            // 清理响应，移除markdown代码块标记
            const cleanedResponse = this.cleanJsonResponse(response);
            return JSON.parse(cleanedResponse);
        } catch (error) {
            this.logger.error('❌ AI页面分析失败:', error);
            return {
                summary: "页面分析失败",
                currentStep: "等待",
                possibleActions: ["wait"],
                confidence: 0.1
            };
        }
    }

    /**
     * 基于Playwright数据规划下一步操作 - 不需要重复分析页面
     */
    async planNextActionFromPlaywrightData(pageState, executionContext) {
        try {
            // 构建基于Playwright分析结果的提示词
            const playwrightInfo = this.buildPlaywrightDataPrompt(pageState);

            const prompt = `
你是一个专业的RPA自动化执行助手。基于Playwright已经分析好的页面数据，规划下一步操作。

任务描述: ${executionContext.taskDescription}
当前目标: ${executionContext.currentObjective}

${playwrightInfo}

已完成的步骤:
${executionContext.completedSteps.map((step, i) => `${i+1}. ${step.action?.action_type || 'unknown'} - ${step.action?.reasoning || 'no reason'}`).join('\n')}

系统操作指南:
${this.systemGuide}

请基于Playwright提供的详细页面元素信息，规划下一步最佳操作。返回JSON格式:
{
    "action_type": "navigate|click|fill|type|wait|screenshot",
    "target_element": {
        "selector": "具体的CSS选择器或ID",
        "description": "元素描述"
    },
    "action_data": {
        "url": "导航URL（如果是navigate）",
        "text": "输入文本（如果是fill/type）",
        "timeout": "等待时间（如果是wait）"
    },
    "reasoning": "选择此操作的详细理由",
    "expected_outcome": "预期结果"
}`;

            const response = await this.modelClient.callMainModel(prompt);

            // 清理响应，移除markdown代码块标记
            const cleanedResponse = this.cleanJsonResponse(response);
            return JSON.parse(cleanedResponse);
        } catch (error) {
            this.logger.error('❌ AI操作规划失败:', error);
            return {
                action_type: "wait",
                target_element: { selector: "body", description: "页面主体" },
                action_data: { timeout: 3000 },
                reasoning: "AI规划失败，执行等待操作",
                expected_outcome: "等待页面稳定"
            };
        }
    }

    /**
     * 执行具体操作 - 使用MCP客户端
     */
    async executeAction(action) {
        try {
            this.logger.info(`🎬 执行操作: ${action.action_type} - ${action.reasoning}`);

            // 使用MCP客户端执行操作 - 基于我的成功经验
            const result = await this.mcpClient.executeAction(action);

            // 如果是截图操作，保存到执行上下文
            if (action.action_type === 'screenshot' && result.success) {
                this.executionContext.screenshots.push({
                    description: action.action_data?.description || '操作截图',
                    timestamp: result.timestamp,
                    filename: result.filename
                });
            }

            return result;
        } catch (error) {
            this.logger.error(`❌ 操作执行失败: ${action.action_type}`, error);
            return {
                action: action,
                success: false,
                message: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * 智能元素查找
     */
    async findElement(targetElement) {
        try {
            // 1. 尝试CSS选择器
            if (targetElement.selector && !targetElement.selector.includes('文本')) {
                try {
                    const element = await this.page.$(targetElement.selector);
                    if (element) return element;
                } catch (e) {
                    // 继续尝试其他方式
                }
            }

            // 2. 尝试文本匹配
            if (targetElement.description || targetElement.selector) {
                const text = targetElement.description || targetElement.selector;
                
                // 尝试按钮文本
                try {
                    const button = await this.page.getByRole('button', { name: text }).first();
                    if (await button.isVisible()) return button;
                } catch (e) {}

                // 尝试链接文本
                try {
                    const link = await this.page.getByRole('link', { name: text }).first();
                    if (await link.isVisible()) return link;
                } catch (e) {}

                // 尝试通用文本匹配
                try {
                    const element = await this.page.getByText(text).first();
                    if (await element.isVisible()) return element;
                } catch (e) {}
            }

            return null;
        } catch (error) {
            this.logger.error('❌ 元素查找失败:', error);
            return null;
        }
    }

    /**
     * 验证操作结果 - 使用Playwright增强验证
     */
    async verifyActionResult(result, action) {
        try {
            // 使用Playwright增强执行器的验证功能
            const verification = await this.executor.verifyAction(action, result);

            return verification;
        } catch (error) {
            return {
                isValid: false,
                hasError: true,
                error: error.message,
                confidence: 0.1
            };
        }
    }

    /**
     * 更新执行上下文
     */
    updateContext(result, verification, action) {
        this.executionContext.completedSteps.push({
            action: action,
            result: result,
            verification: verification,
            timestamp: new Date().toISOString()
        });

        if (verification.hasError) {
            this.executionContext.encounteredIssues.push({
                action: action,
                error: verification.error,
                timestamp: new Date().toISOString()
            });
        }
    }

    /**
     * 错误处理
     */
    async handleError(error, context) {
        this.logger.warn(`⚠️ 处理错误: ${error}`);
        // 简单的错误恢复：等待一段时间
        await this.page.waitForTimeout(2000);
    }

    /**
     * 判断任务是否完成
     */
    isTaskComplete(currentState, task) {
        // 简单的完成判断：执行了足够的步骤
        return this.executionContext.completedSteps.length >= 5;
    }

    /**
     * 生成执行报告
     */
    async generateExecutionReport(context, executionTime) {
        const successfulSteps = context.completedSteps.filter(step => step.result.success);
        const failedSteps = context.completedSteps.filter(step => !step.result.success);

        return {
            summary: `任务执行完成，成功步骤: ${successfulSteps.length}，失败步骤: ${failedSteps.length}`,
            executionTime,
            successfulSteps: successfulSteps.length,
            failedSteps: failedSteps.length,
            completedSteps: context.completedSteps,
            issues: context.encounteredIssues,
            screenshots: context.screenshots
        };
    }

    /**
     * 构建Playwright数据提示词 - 直接使用结构化数据
     */
    buildPlaywrightDataPrompt(pageState) {
        let prompt = `
Playwright页面分析结果 (已完成，无需重复分析):
- URL: ${pageState.url}
- 标题: ${pageState.title}
- 时间戳: ${new Date(pageState.timestamp).toLocaleString()}

可操作元素详情:`;

        // 输入框信息 - 最重要，优先展示
        if (pageState.inputs && pageState.inputs.length > 0) {
            prompt += `\n\n🔤 输入框 (${pageState.inputs.length}个) - 可以使用fill或type操作:`;
            pageState.inputs.forEach((input, i) => {
                prompt += `\n  ${i+1}. ID: "${input.id}" | Name: "${input.name}" | 占位符: "${input.placeholder}"`;
                prompt += `\n     类型: ${input.inputType || input.type} | 可用: ${input.isEnabled} | 当前值: "${input.value}"`;
                if (input.id) prompt += `\n     推荐选择器: #${input.id}`;
                else if (input.name) prompt += `\n     推荐选择器: [name="${input.name}"]`;
            });
        }

        // 按钮信息
        if (pageState.buttons && pageState.buttons.length > 0) {
            prompt += `\n\n🔘 按钮 (${pageState.buttons.length}个) - 可以使用click操作:`;
            pageState.buttons.forEach((btn, i) => {
                prompt += `\n  ${i+1}. 文本: "${btn.text}" | 可用: ${btn.isEnabled}`;
                if (btn.boundingBox) {
                    prompt += `\n     位置: x=${btn.boundingBox.x}, y=${btn.boundingBox.y}`;
                }
            });
        }

        // 链接信息
        if (pageState.links && pageState.links.length > 0) {
            prompt += `\n\n🔗 重要链接 (前5个) - 可以使用click操作:`;
            pageState.links.slice(0, 5).forEach((link, i) => {
                prompt += `\n  ${i+1}. 文本: "${link.text}" | 链接: ${link.href}`;
            });
        }

        // 表单信息
        if (pageState.forms && pageState.forms.length > 0) {
            prompt += `\n\n📝 表单 (${pageState.forms.length}个):`;
            pageState.forms.forEach((form, i) => {
                prompt += `\n  ${i+1}. 动作: ${form.action} | 方法: ${form.method}`;
                if (form.fields && form.fields.length > 0) {
                    prompt += `\n     包含字段: ${form.fields.map(f => f.name || f.type).join(', ')}`;
                }
            });
        }

        return prompt;
    }

    /**
     * 构建页面信息提示词 - 利用Playwright分析结果 (保留备用)
     */
    buildPageInfoPrompt(pageState) {
        let prompt = `
页面基本信息:
- URL: ${pageState.url}
- 标题: ${pageState.title}
- 视口大小: ${JSON.stringify(pageState.viewport)}

Playwright分析结果:`;

        // 按钮信息
        if (pageState.buttons && pageState.buttons.length > 0) {
            prompt += `\n\n可点击按钮 (${pageState.buttons.length}个):`;
            pageState.buttons.slice(0, 5).forEach((btn, i) => {
                prompt += `\n  ${i+1}. 文本: "${btn.text}" | 可用: ${btn.isEnabled} | 位置: ${JSON.stringify(btn.boundingBox)}`;
            });
        }

        // 输入框信息
        if (pageState.inputs && pageState.inputs.length > 0) {
            prompt += `\n\n输入框 (${pageState.inputs.length}个):`;
            pageState.inputs.slice(0, 5).forEach((input, i) => {
                prompt += `\n  ${i+1}. 类型: ${input.inputType || input.type} | 占位符: "${input.placeholder}" | 名称: "${input.name}" | ID: "${input.id}" | 可用: ${input.isEnabled}`;
            });
        }

        // 链接信息
        if (pageState.links && pageState.links.length > 0) {
            prompt += `\n\n重要链接 (前5个):`;
            pageState.links.slice(0, 5).forEach((link, i) => {
                prompt += `\n  ${i+1}. 文本: "${link.text}" | 链接: ${link.href}`;
            });
        }

        // 表单信息
        if (pageState.forms && pageState.forms.length > 0) {
            prompt += `\n\n表单 (${pageState.forms.length}个):`;
            pageState.forms.forEach((form, i) => {
                prompt += `\n  ${i+1}. 动作: ${form.action} | 方法: ${form.method} | 字段数: ${form.fields?.length || 0}`;
            });
        }

        // 页面状态
        if (pageState.pageState) {
            prompt += `\n\n页面状态:`;
            prompt += `\n- 正在加载: ${pageState.pageState.isLoading}`;
            prompt += `\n- 有错误: ${pageState.pageState.hasErrors}`;
            prompt += `\n- 有模态框: ${pageState.pageState.hasModals}`;
        }

        return prompt;
    }

    /**
     * 清理JSON响应，移除markdown代码块标记
     */
    cleanJsonResponse(response) {
        try {
            // 移除markdown代码块标记
            let cleaned = response.trim();

            // 移除开头的```json或```
            cleaned = cleaned.replace(/^```(?:json)?\s*\n?/i, '');

            // 移除结尾的```
            cleaned = cleaned.replace(/\n?\s*```\s*$/i, '');

            // 移除其他可能的markdown标记
            cleaned = cleaned.replace(/^\s*```\s*$/gm, '');

            // 尝试提取JSON部分
            const jsonMatch = cleaned.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                cleaned = jsonMatch[0];
            }

            // 移除多余的换行和空格
            cleaned = cleaned.replace(/\n\s*\n/g, '\n').trim();

            return cleaned;
        } catch (error) {
            this.logger.warn('⚠️ 清理JSON响应失败，使用原始响应:', error);
            return response;
        }
    }

    /**
     * 清理资源 - MCP模式
     */
    async cleanup() {
        try {
            if (this.mcpClient) {
                await this.mcpClient.cleanup();
                this.logger.info('✅ MCP客户端已清理');
            }
        } catch (error) {
            this.logger.error('❌ 清理资源失败:', error);
        }
    }
}

module.exports = AIExecutionController;
