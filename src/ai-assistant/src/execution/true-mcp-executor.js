/**
 * 真正的MCP执行器 - 基于Microsoft官方Playwright MCP服务器
 * 实现真正的MCP客户端-服务器架构
 */

const { spawn } = require('child_process');
const axios = require('axios');
const logger = require('../utils/logger');

class TrueMCPExecutor {
    constructor() {
        this.mcpServerProcess = null;
        this.mcpServerPort = 8931;
        this.mcpServerUrl = `http://127.0.0.1:${this.mcpServerPort}`;
        this.isServerRunning = false;
        
        // qwen-turbo配置
        this.modelConfig = {
            apiKey: process.env.DASHSCOPE_API_KEY,
            baseUrl: process.env.DASHSCOPE_BASE_URL,
            model: 'qwen-turbo'
        };
    }

    /**
     * 启动真正的Playwright MCP服务器
     */
    async startMCPServer() {
        try {
            logger.info('🚀 启动Microsoft官方Playwright MCP服务器...');
            
            // 启动Microsoft官方Playwright MCP服务器进程
            this.mcpServerProcess = spawn('npx', [
                '@playwright/mcp',
                '--port', this.mcpServerPort.toString(),
                '--headless',
                '--isolated',
                '--host', '127.0.0.1'
            ], {
                stdio: ['pipe', 'pipe', 'pipe'],
                env: { ...process.env }
            });

            // 监听服务器输出
            this.mcpServerProcess.stdout.on('data', (data) => {
                logger.info(`MCP服务器: ${data.toString().trim()}`);
            });

            this.mcpServerProcess.stderr.on('data', (data) => {
                logger.error(`MCP服务器错误: ${data.toString().trim()}`);
            });

            // 等待服务器启动
            await this.waitForServerReady();
            
            this.isServerRunning = true;
            logger.info('✅ Microsoft官方Playwright MCP服务器启动成功');
            
            return true;
        } catch (error) {
            logger.error('❌ 启动MCP服务器失败:', error);
            throw error;
        }
    }

    /**
     * 等待MCP服务器就绪
     */
    async waitForServerReady(maxAttempts = 10) {
        // 等待服务器启动
        await new Promise(resolve => setTimeout(resolve, 3000));

        for (let i = 0; i < maxAttempts; i++) {
            try {
                // 尝试调用tools/list来测试MCP服务器
                const testMessage = {
                    jsonrpc: "2.0",
                    id: 1,
                    method: "tools/list"
                };

                const response = await axios.post(`${this.mcpServerUrl}/mcp`, testMessage, {
                    timeout: 3000,
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json, text/event-stream'
                    }
                });

                if (response.data && response.data.result) {
                    logger.info('✅ MCP服务器响应正常，工具列表获取成功');
                    return true;
                }
            } catch (error) {
                // 服务器还未就绪，继续等待
                logger.info(`⏳ 等待MCP服务器就绪... (${i + 1}/${maxAttempts})`);
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }
        throw new Error('MCP服务器启动超时');
    }

    /**
     * 停止MCP服务器
     */
    async stopMCPServer() {
        if (this.mcpServerProcess) {
            logger.info('🛑 停止MCP服务器...');
            this.mcpServerProcess.kill();
            this.mcpServerProcess = null;
            this.isServerRunning = false;
            logger.info('✅ MCP服务器已停止');
        }
    }

    /**
     * 生成真正的Playwright MCP工具定义
     */
    generatePlaywrightMCPTools() {
        return [
            {
                type: 'function',
                function: {
                    name: 'browser_navigate',
                    description: '导航到指定URL',
                    parameters: {
                        type: 'object',
                        properties: {
                            url: {
                                type: 'string',
                                description: '目标网址'
                            }
                        },
                        required: ['url']
                    }
                }
            },
            {
                type: 'function',
                function: {
                    name: 'browser_snapshot',
                    description: '获取页面快照(结构化文本，包含所有可交互元素的ref属性)',
                    parameters: {
                        type: 'object',
                        properties: {},
                        required: []
                    }
                }
            },
            {
                type: 'function',
                function: {
                    name: 'browser_click',
                    description: '点击页面元素',
                    parameters: {
                        type: 'object',
                        properties: {
                            element: {
                                type: 'string',
                                description: '元素描述'
                            },
                            ref: {
                                type: 'string',
                                description: '元素的ref属性，如e14, e685等'
                            }
                        },
                        required: ['element', 'ref']
                    }
                }
            },
            {
                type: 'function',
                function: {
                    name: 'browser_type',
                    description: '在输入框中输入文本',
                    parameters: {
                        type: 'object',
                        properties: {
                            element: {
                                type: 'string',
                                description: '输入框描述'
                            },
                            ref: {
                                type: 'string',
                                description: '输入框的ref属性'
                            },
                            text: {
                                type: 'string',
                                description: '要输入的文本'
                            }
                        },
                        required: ['element', 'ref', 'text']
                    }
                }
            },
            {
                type: 'function',
                function: {
                    name: 'browser_take_screenshot',
                    description: '截图保存',
                    parameters: {
                        type: 'object',
                        properties: {
                            filename: {
                                type: 'string',
                                description: '文件名(可选)'
                            }
                        },
                        required: []
                    }
                }
            },
            {
                type: 'function',
                function: {
                    name: 'browser_wait_for',
                    description: '等待指定时间',
                    parameters: {
                        type: 'object',
                        properties: {
                            time: {
                                type: 'number',
                                description: '等待秒数'
                            }
                        },
                        required: ['time']
                    }
                }
            }
        ];
    }

    /**
     * 调用真正的MCP服务器 (使用MCP协议)
     */
    async callMCPServer(toolName, params) {
        try {
            logger.info(`🛠️ 调用真正的MCP工具: ${toolName}`);
            logger.info(`📋 参数:`, params);

            // 构建MCP协议消息
            const mcpMessage = {
                jsonrpc: "2.0",
                id: Date.now(),
                method: "tools/call",
                params: {
                    name: toolName,
                    arguments: params
                }
            };

            const response = await axios.post(`${this.mcpServerUrl}/mcp`, mcpMessage, {
                timeout: 30000,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json, text/event-stream'
                }
            });

            logger.info(`✅ MCP工具调用成功: ${toolName}`);
            return {
                success: true,
                tool: toolName,
                result: response.data.result,
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            logger.error(`❌ MCP工具调用失败: ${toolName}`, error.message);
            return {
                success: false,
                tool: toolName,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * 执行工具调用
     */
    async executeToolCall(toolCall) {
        const { name, arguments: args } = toolCall.function;
        const parsedArgs = JSON.parse(args);

        // 映射工具名称到MCP服务器端点
        const toolMapping = {
            'browser_navigate': 'browser_navigate',
            'browser_snapshot': 'browser_snapshot', 
            'browser_click': 'browser_click',
            'browser_type': 'browser_type',
            'browser_take_screenshot': 'browser_take_screenshot',
            'browser_wait_for': 'browser_wait_for'
        };

        const mcpToolName = toolMapping[name];
        if (!mcpToolName) {
            throw new Error(`不支持的工具: ${name}`);
        }

        return await this.callMCPServer(mcpToolName, parsedArgs);
    }

    /**
     * 执行真正的RPA任务
     */
    async executeRealRPATask(ticket) {
        try {
            logger.info(`🚀 开始执行真正的MCP架构RPA任务: ${ticket.id}`);

            // 1. 启动MCP服务器
            if (!this.isServerRunning) {
                await this.startMCPServer();
            }

            // 2. 构建任务提示词
            const prompt = this.buildTaskPrompt(ticket);
            
            // 3. 生成工具定义
            const tools = this.generatePlaywrightMCPTools();

            // 4. 执行AI工具调用循环
            const result = await this.executeAIToolLoop(prompt, tools);

            logger.info(`✅ 真正的MCP架构RPA任务完成: ${ticket.id}`);
            
            return {
                success: result.success,
                ticketId: ticket.id,
                finalMessage: result.finalMessage,
                toolResults: result.results,
                iterations: result.iterations,
                error: result.error
            };

        } catch (error) {
            logger.error(`❌ 真正的MCP架构RPA任务失败: ${ticket.id}`, error);
            return {
                success: false,
                ticketId: ticket.id,
                error: error.message
            };
        }
    }

    /**
     * 构建任务提示词
     */
    buildTaskPrompt(ticket) {
        return `# 真正的RPA自动化任务

## 任务信息
- 工单ID: ${ticket.id}
- 标题: ${ticket.title}
- 内容: ${ticket.content}

## 任务目标
请使用提供的Playwright MCP工具完成以下任务：
${ticket.content}

## 工作流程
1. 首先导航到 https://uat-merchant.aomiapp.com/#/bdlogin
2. 获取页面快照分析页面结构
3. 根据页面内容执行相应操作
4. 在每个关键步骤后截图记录
5. 完成任务后提供总结

## 重要提醒
- 使用browser_snapshot获取页面结构
- 使用页面快照中的ref属性精确定位元素
- 每次操作后检查结果，根据实际情况调整策略
- 如遇到登录页面，提醒用户手动登录后继续

请开始执行任务。`;
    }

    /**
     * 执行AI工具调用循环
     */
    async executeAIToolLoop(prompt, tools, maxIterations = 10) {
        let iteration = 0;
        let conversationHistory = [{ role: 'user', content: prompt }];
        let results = [];

        while (iteration < maxIterations) {
            iteration++;
            logger.info(`🔄 真正的MCP工具调用迭代 ${iteration}/${maxIterations}`);

            try {
                // 调用qwen-plus
                const response = await axios.post(`${this.modelConfig.baseUrl}/chat/completions`, {
                    model: this.modelConfig.model,
                    messages: conversationHistory,
                    tools: tools,
                    tool_choice: 'auto',
                    temperature: 0.1,
                    max_tokens: 2000
                }, {
                    headers: {
                        'Authorization': `Bearer ${this.modelConfig.apiKey}`,
                        'Content-Type': 'application/json'
                    }
                });

                const message = response.data.choices[0].message;
                conversationHistory.push(message);

                // 检查是否有工具调用
                if (message.tool_calls && message.tool_calls.length > 0) {
                    logger.info(`🔧 qwen-plus请求调用 ${message.tool_calls.length} 个真正的MCP工具`);

                    // 执行每个工具调用
                    for (const toolCall of message.tool_calls) {
                        const toolResult = await this.executeToolCall(toolCall);
                        results.push(toolResult);

                        // 将工具执行结果添加到对话历史
                        conversationHistory.push({
                            role: 'tool',
                            tool_call_id: toolCall.id,
                            content: JSON.stringify(toolResult)
                        });
                    }
                } else {
                    // 没有工具调用，任务完成
                    logger.info('✅ qwen-plus完成任务，无需更多工具调用');
                    return {
                        success: true,
                        finalMessage: message.content,
                        results: results,
                        iterations: iteration
                    };
                }

            } catch (error) {
                logger.error(`❌ 真正的MCP工具调用迭代 ${iteration} 失败:`, error);
                return {
                    success: false,
                    error: error.message,
                    results: results,
                    iterations: iteration
                };
            }
        }

        logger.warn('⚠️ 达到最大迭代次数，任务可能未完成');
        return {
            success: false,
            error: '达到最大迭代次数',
            results: results,
            iterations: iteration
        };
    }

    /**
     * 清理资源
     */
    async cleanup() {
        await this.stopMCPServer();
    }
}

module.exports = TrueMCPExecutor;
