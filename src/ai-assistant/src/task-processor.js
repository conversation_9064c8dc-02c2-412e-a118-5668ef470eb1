const axios = require('axios');
const logger = require('./utils/logger');
const TaskParser = require('./task-parser');
const AIExecutionController = require('./execution/ai-execution-controller');
const CoordinatorAgent = require('./agents/coordinator-agent');
const EnhancedExecutionController = require('./enhanced/enhanced-execution-controller');
const { MCPClientV2 } = require('./playwright-mcp/mcp-client-v2');
const MCPTaskExecutor = require('./execution/mcp-task-executor');
const RealMCPExecutor = require('./execution/real-mcp-executor');
const { IntelligentExecutor } = require('./core/intelligent-executor');
// SimpleExecutor 将在需要时动态导入

// 加载环境变量
require('dotenv').config();

// 高效模型客户端 - 专为RPA项目优化
class RPAModelClient {
    constructor() {
        this.apiKey = process.env.DASHSCOPE_API_KEY;
        this.baseUrl = process.env.DASHSCOPE_BASE_URL;
        this.mainModel = process.env.MAIN_MODEL || 'qwen-turbo';

        if (!this.apiKey) {
            console.error('❌ 环境变量检查:');
            console.error('DASHSCOPE_API_KEY:', this.apiKey ? '已设置' : '未设置');
            console.error('DASHSCOPE_BASE_URL:', this.baseUrl ? '已设置' : '未设置');
            throw new Error('请在.env文件中设置DASHSCOPE_API_KEY');
        }
    }

    async callMainModel(prompt) {
        try {
            logger.info('🧠 qwen AI分析任务并规划操作...');

            const response = await axios.post(`${this.baseUrl}/chat/completions`, {
                model: this.mainModel,
                messages: [{
                    role: 'user',
                    content: prompt + '\n\n重要：请只返回纯JSON格式，不要包含markdown标记或其他文字。'
                }],
                temperature: 0.1,
                max_tokens: 4000
            }, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                }
            });

            const content = response.data.choices[0].message.content;
            logger.info('✅ qwen AI分析完成');
            return content;
        } catch (error) {
            logger.error('❌ qwen AI调用失败:', error.message);
            throw error;
        }
    }

    // 启用真正的视觉模型分析
    async callVisionModel(prompt, screenshot) {
        try {
            logger.info('👁️ qwen-VL视觉分析页面...');

            const messages = [{
                role: 'user',
                content: [
                    {
                        type: 'text',
                        text: prompt + '\n\n请只返回JSON格式的响应，不要包含任何其他文字或markdown标记。'
                    },
                    {
                        type: 'image_url',
                        image_url: { url: `data:image/png;base64,${screenshot}` }
                    }
                ]
            }];

            const response = await fetch(`${this.baseUrl}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: 'qwen-vl-plus',
                    messages: messages,
                    temperature: 0.1,
                    max_tokens: 1000
                })
            });

            const data = await response.json();
            const content = data.choices[0].message.content;

            logger.info('✅ qwen-VL视觉分析完成');
            return content;
        } catch (error) {
            logger.error('❌ qwen-VL视觉分析失败:', error);
            // 备用：返回基础分析
            return JSON.stringify({
                summary: "视觉分析失败，使用Playwright数据",
                currentStep: "基于结构化数据决策",
                possibleActions: ["使用Playwright元素"],
                confidence: 0.5
            });
        }
    }
}

class TaskProcessor {
    constructor() {
        this.workorderApiUrl = process.env.WORKORDER_API_URL || 'http://localhost:3001';
        this.taskParser = new TaskParser();

        // 使用MCP架构的AI-First RPA系统
        this.modelClient = new RPAModelClient();
        this.aiController = new AIExecutionController(logger);
        this.coordinatorAgent = new CoordinatorAgent(this.modelClient, logger, this.workorderApiUrl);
        this.enhancedController = new EnhancedExecutionController(logger);

        // 简化执行器 - 只在需要时创建，避免多余的浏览器实例
        this.simpleExecutor = null;

        // 保留原有组件作为备用
        this.mcpClient = new MCPClientV2(logger);
        this.mcpExecutor = new MCPTaskExecutor(this.mcpClient, logger);
        this.realMCPExecutor = new RealMCPExecutor(this.mcpClient);
        // 配置对象
        const config = {
            qwen: {
                baseUrl: process.env.DASHSCOPE_BASE_URL || 'https://dashscope.aliyuncs.com/compatible-mode/v1',
                apiKey: process.env.DASHSCOPE_API_KEY,
                model: process.env.MAIN_MODEL || 'qwen-turbo'
            }
        };

        this.intelligentExecutor = new IntelligentExecutor(this.mcpClient, config, null, this);

        this.queueSize = 0;
        this.processedCount = 0;

        // 实时执行步骤记录
        this.executionSteps = [];
        this.maxStepsHistory = 50; // 最多保存50条步骤记录
        this.isInitialized = false;
        this.useSimpleMode = false; // 禁用简化模式
        this.useMCPMode = false; // 禁用旧的MCP模式
        this.useIntelligentMode = true; // 启用智能AI驱动模式
        this.useEnhancedMode = false; // 禁用旧的增强模式

        // 任务历史记录
        this.taskHistory = [];
        this.maxHistorySize = 50;
    }

    async initialize() {
        if (!this.isInitialized) {
            logger.info('🚀 初始化简化版AI-First RPA系统...');

            if (this.useSimpleMode) {
                // 只在需要时创建和初始化简化执行器
                if (!this.simpleExecutor) {
                    const { SimpleExecutor } = require('./execution/simple-executor');
                    this.simpleExecutor = new SimpleExecutor(logger);
                }
                await this.simpleExecutor.initialize();
                logger.info('✅ 简化执行器初始化完成');
            } else if (this.useIntelligentMode) {
                // 初始化智能AI驱动模式
                await this.mcpClient.initialize();
                await this.intelligentExecutor.initialize();
                logger.info('✅ 智能AI驱动架构初始化完成');
            } else if (this.useMCPMode) {
                // 初始化MCP客户端
                await this.mcpClient.initialize();
                logger.info('✅ MCP客户端初始化完成');

                // 初始化AI控制器（使用MCP架构）
                await this.aiController.initialize(this.modelClient);
                logger.info('✅ MCP架构AI控制器初始化完成');
            } else if (this.useEnhancedMode) {
                await this.enhancedController.initialize(this.modelClient);
                logger.info('✅ 增强版执行控制器初始化完成');
            } else {
                await this.aiController.initialize(this.modelClient);
                logger.info('✅ 标准AI控制器初始化完成');
            }

            this.isInitialized = true;
            logger.info('✅ 简化版AI-First RPA系统初始化完成');
        }
    }

    getQueueSize() {
        return this.queueSize;
    }

    getProcessedCount() {
        return this.processedCount;
    }

    getStatus() {
        return {
            isRunning: this.isInitialized,
            queueSize: this.queueSize,
            processedCount: this.processedCount,
            mode: this.useIntelligentMode ? 'intelligent' :
                  this.useSimpleMode ? 'simple' :
                  this.useMCPMode ? 'mcp' :
                  this.useEnhancedMode ? 'enhanced' : 'standard'
        };
    }

    /**
     * 获取最近任务
     */
    getRecentTasks(limit = 5) {
        return this.taskHistory
            .slice(-limit)
            .reverse()
            .map(task => ({
                ...task,
                timestamp: task.timestamp || new Date().toISOString()
            }));
    }

    /**
     * 添加任务到历史记录
     */
    addTaskToHistory(task) {
        const taskRecord = {
            id: task.id || Date.now(),
            ticketId: task.ticketId,
            title: task.title || '任务处理',
            description: task.description || task.action || '执行中...',
            status: task.status || 'processing',
            timestamp: new Date().toISOString(),
            iteration: task.iteration || 0
        };

        this.taskHistory.push(taskRecord);

        // 保持历史记录大小限制
        if (this.taskHistory.length > this.maxHistorySize) {
            this.taskHistory = this.taskHistory.slice(-this.maxHistorySize);
        }
    }

    async processTicket(ticket) {
        logger.info(`🎯 开始处理RPA工单: ${ticket.id} - ${ticket.title}`);

        // 添加任务开始记录
        this.addTaskToHistory({
            ticketId: ticket.id,
            title: ticket.title,
            description: '开始处理工单',
            status: 'started'
        });

        try {
            // 确保AI系统已初始化
            await this.initialize();

            // 1. 使用真正的MCP架构执行任务
            logger.info(`🚀 开始真正的MCP架构AI-First RPA执行...`);
            const startTime = Date.now();

            let result;
            let taskForReport = ticket; // 用于报告生成的任务对象

            if (this.useSimpleMode) {
                // 使用简化执行器 - 绕过复杂的MCP协议
                logger.info(`🎯 使用简化执行器处理工单: ${ticket.id}`);
                result = await this.simpleExecutor.executeTask(ticket);
                taskForReport = { type: 'simple_task', ...ticket }; // 简化任务
            } else if (this.useIntelligentMode) {
                // 使用智能AI驱动执行器 - 完全消除硬编码
                logger.info(`🧠 使用智能AI驱动执行器处理工单: ${ticket.id}`);
                result = await this.intelligentExecutor.executeTask(ticket);
                taskForReport = { type: 'intelligent_task', ...ticket }; // 智能任务
            } else if (this.useMCPMode) {
                // 使用真正的MCP执行器 - 让qwen-turbo真正调用Playwright工具
                logger.info(`🎯 使用真正的MCP执行器处理工单: ${ticket.id}`);
                result = await this.realMCPExecutor.executeRealRPATask(ticket);
                taskForReport = { type: 'real_mcp_task', ...ticket }; // 真正的MCP任务
            } else if (this.useEnhancedMode) {
                // 回退到增强版执行控制器
                const rpaTask = this.convertTicketToRPATask(ticket);
                result = await this.enhancedController.executeEnhancedTask(rpaTask);
                taskForReport = rpaTask; // RPA任务
            } else {
                // 回退到标准AI执行控制器
                const rpaTask = this.convertTicketToRPATask(ticket);
                result = await this.aiController.executeTask(rpaTask);
                taskForReport = rpaTask; // RPA任务
            }

            const executionTime = Date.now() - startTime;
            logger.info(`⏱️ RPA任务执行完成，耗时: ${(executionTime/1000).toFixed(2)}秒`);

            // 3. 生成详细的执行报告
            const report = this.generateAIRPAReport(ticket, taskForReport, result, executionTime);

            // 4. 更新工单状态
            if (result.success) {
                await axios.patch(`${this.workorderApiUrl}/api/tickets/${ticket.id}/status`, {
                    status: '已完成',
                    report: report.summary,
                    notes: `🎉 真正的MCP架构RPA执行成功！qwen-turbo智能调用了${result.toolResults?.length || 0}个Playwright工具，执行${result.iterations || 0}次AI迭代，实现真实的浏览器自动化`
                });

                this.processedCount++;
                logger.info(`✅ RPA工单处理成功: ${ticket.id}`);

                return {
                    success: true,
                    ticketId: ticket.id,
                    executionTime: executionTime,
                    stepsCompleted: result.stepsCompleted,
                    report: report
                };
            } else {
                await axios.patch(`${this.workorderApiUrl}/api/tickets/${ticket.id}/status`, {
                    status: '处理失败',
                    notes: `真正的MCP架构AI-First RPA执行失败: ${result.error}`
                });

                logger.error(`❌ RPA工单处理失败: ${ticket.id} - ${result.error}`);

                return {
                    success: false,
                    ticketId: ticket.id,
                    error: result.error,
                    report: result.report
                };
            }

        } catch (error) {
            logger.error(`❌ RPA工单处理异常: ${ticket.id}`, error);

            // 更新工单状态为异常
            await axios.patch(`${this.workorderApiUrl}/api/tickets/${ticket.id}/status`, {
                status: '处理失败',
                notes: `系统异常: ${error.message}`
            });

            return {
                success: false,
                ticketId: ticket.id,
                error: error.message
            };
        }
    }

    /**
     * 将工单转换为AI-First RPA任务
     */
    convertTicketToRPATask(ticket) {
        // 分析工单内容，提取关键信息
        const content = ticket.content || '';
        const title = ticket.title || '';

        // 构建RPA任务描述
        let description = `RPA工单任务: ${title}`;
        if (content) {
            description += ` - ${content}`;
        }

        // 根据工单内容推断初始操作
        let initialAction = null;

        // 检查是否包含网址
        const urlMatch = content.match(/(https?:\/\/[^\s]+)/i);
        if (urlMatch) {
            initialAction = {
                action_type: 'navigate',
                action_data: { url: urlMatch[1] },
                reasoning: `根据工单要求导航到指定网址: ${urlMatch[1]}`
            };
        } else if (content.includes('百度') || content.includes('搜索')) {
            initialAction = {
                action_type: 'navigate',
                action_data: { url: 'https://www.baidu.com' },
                reasoning: '根据工单要求导航到百度进行搜索操作'
            };
        }

        return {
            id: `rpa_ticket_${ticket.id}_${Date.now()}`,
            description: description,
            objective: `完成工单${ticket.id}的自动化任务要求`,
            ticketId: ticket.id,
            originalContent: content,
            initialAction: initialAction
        };
    }

    /**
     * 生成AI-First RPA执行报告
     */
    generateAIRPAReport(ticket, task, result, executionTime) {
        const report = {
            // 基本信息
            ticketId: ticket.id,
            ticketTitle: ticket.title,
            executionTime: executionTime,
            timestamp: new Date().toISOString(),

            // 执行结果
            success: result.success,
            stepsCompleted: result.stepsCompleted || result.completedActions || result.totalActions || 0,

            // 详细摘要
            summary: result.success
                ? `✅ ${task?.type === 'mcp_browser_automation' ? 'MCP架构' : 'AI-First RPA'}执行成功！完成${result.stepsCompleted || result.completedActions || result.totalActions || 0}个操作步骤，耗时${(executionTime/1000).toFixed(2)}秒`
                : `❌ ${task?.type === 'mcp_browser_automation' ? 'MCP架构' : 'AI-First RPA'}执行失败：${result.error}`,

            // 技术详情
            technology: {
                architecture: task?.type === 'mcp_browser_automation' ? 'MCP架构' : 'AI-First RPA',
                aiModel: 'qwen-turbo',
                automation: task?.type === 'mcp_browser_automation' ? 'Playwright MCP' : 'Playwright原生能力',
                approach: task?.type === 'mcp_browser_automation' ? '页面快照 + 精确元素定位' : 'Playwright分析页面 → qwen AI决策 → Playwright执行操作'
            }
        };

        // 添加详细的执行步骤
        if (result.report && result.report.completedSteps) {
            report.executionSteps = result.report.completedSteps.map((step, index) => ({
                stepNumber: index + 1,
                actionType: step.action.action_type,
                description: step.action.reasoning,
                target: step.action.target_element?.description || '无特定目标',
                result: step.result.success ? '成功' : '失败',
                message: step.result.message,
                playwrightMethod: step.result.playwrightMethod || '未知',
                timestamp: step.timestamp
            }));
        }

        // 添加截图信息
        if (result.report && result.report.screenshots) {
            report.screenshots = result.report.screenshots.map((screenshot, index) => ({
                index: index + 1,
                description: screenshot.description,
                timestamp: screenshot.timestamp,
                url: screenshot.url,
                title: screenshot.title,
                dataSize: screenshot.data?.length || 0
            }));
        }

        // 添加问题信息
        if (result.report && result.report.issues) {
            report.issues = result.report.issues.map(issue => ({
                error: issue.error,
                timestamp: issue.timestamp
            }));
        }

        return report;
    }

    // 保留原有的报告生成方法作为备用
    generateReport(ticket, tasks, results) {
        const successCount = results.filter(r => r.success).length;
        const totalTime = results.reduce((sum, r) => sum + (r.executionTime || 0), 0);

        return {
            summary: `成功完成 ${successCount}/${tasks.length} 个任务`,
            totalExecutionTime: totalTime,
            tasks: tasks.map((task, index) => ({
                description: task.description,
                result: results[index],
                success: results[index].success
            })),
            screenshots: results.filter(r => r.screenshot).map(r => r.screenshot),
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 使用AI生成MCP任务 - 核心创新功能
     */
    async generateMCPTask(ticket) {
        try {
            logger.info(`🧠 使用AI分析工单并生成MCP任务: ${ticket.id}`);

            // 读取系统指南
            const systemGuide = await this.loadSystemGuide();

            // 构建AI提示词
            const prompt = this.buildMCPPrompt(ticket, systemGuide);

            // 调用AI模型生成MCP任务
            const aiResponse = await this.modelClient.callMainModel(prompt);

            // 解析AI响应
            const mcpTask = this.parseMCPResponse(aiResponse, ticket);

            logger.info(`✅ MCP任务生成成功: ${mcpTask.id}`);
            return mcpTask;

        } catch (error) {
            logger.error(`❌ MCP任务生成失败: ${ticket.id}`, error);

            // 回退到基础任务
            return this.createFallbackMCPTask(ticket);
        }
    }

    /**
     * 构建MCP提示词 - 包含完整的Playwright工具描述
     */
    buildMCPPrompt(ticket, systemGuide) {
        return `# RPA自动化助手

## 角色定义
你是专业的RPA自动化助手，负责分析工单并生成基于Playwright MCP架构的浏览器自动化任务。

## 工单信息
- 标题: ${ticket.title}
- 内容: ${ticket.content}
- 工单ID: ${ticket.id}

## 系统操作指引
${systemGuide.substring(0, 2000)}

## Playwright MCP工具描述

### 可用工具列表

#### 1. browser_navigate_Playwright
**功能**: 导航到指定URL
**参数**:
- url (string): 目标网址
**示例**: {"type": "mcp_navigate", "url": "https://uat-merchant.aomiapp.com/#/bdlogin"}

#### 2. browser_snapshot_Playwright
**功能**: 获取页面快照(结构化文本，非图像)
**参数**: 无
**返回**: 包含所有可交互元素的结构化文本，每个元素都有ref属性用于精确定位
**示例**: {"type": "mcp_snapshot", "description": "获取页面快照"}

#### 3. browser_click_Playwright
**功能**: 点击页面元素
**参数**:
- element (string): 元素描述
- ref (string): 元素的ref属性，如"e14", "e685"等
**示例**: {"type": "mcp_click", "element": "商品管理", "ref": "e14"}

#### 4. browser_type_Playwright
**功能**: 在输入框中输入文本
**参数**:
- element (string): 输入框描述
- ref (string): 输入框的ref属性
- text (string): 要输入的文本
**示例**: {"type": "mcp_type", "element": "搜索框", "ref": "e204", "text": "咖哩焗魚飯"}

#### 5. browser_wait_for_Playwright
**功能**: 等待指定时间
**参数**:
- time (number): 等待秒数
**示例**: {"type": "mcp_wait", "time": 3}

#### 6. browser_take_screenshot_Playwright
**功能**: 截图保存
**参数**:
- filename (string, 可选): 文件名
**示例**: {"type": "mcp_screenshot", "description": "截图确认"}

### 重要特征
- **精确定位**: 使用ref属性(如e14, e154, e685)进行100%准确的元素定位
- **快照驱动**: 页面快照是结构化文本，包含所有元素信息
- **毫秒响应**: 无需AI视觉模型，纯文本处理
- **自动错误处理**: MCP层自动处理浏览器相关问题

## 工作要求

### 1. 工单内容完整性检查
- 检查工单是否包含足够信息执行任务
- 如信息不足，返回status: "need_info"并说明需要补充的信息

### 2. 任务生成要求
- 必须包含截图步骤用于验证
- 严格按照system_guide.md的操作流程
- 使用正确的ref属性定位元素
- 包含完整的导航和操作步骤

### 3. 安全要求
- 涉及登录时要求用户手动完成
- 敏感操作需要确认步骤

## 输出Schema

### 成功情况
\`\`\`json
{
  "status": "ready",
  "tasks": [
    {
      "id": "task_工单ID",
      "type": "mcp_browser_automation",
      "description": "任务描述",
      "data": {
        "actions": [
          {
            "type": "mcp_navigate",
            "url": "目标网址",
            "description": "操作描述"
          },
          {
            "type": "mcp_snapshot",
            "description": "获取页面快照"
          },
          {
            "type": "mcp_click",
            "element": "元素描述",
            "ref": "元素ref属性",
            "description": "操作描述"
          },
          {
            "type": "mcp_type",
            "element": "输入框描述",
            "ref": "输入框ref属性",
            "text": "输入内容",
            "description": "操作描述"
          },
          {
            "type": "mcp_screenshot",
            "description": "截图确认"
          }
        ]
      }
    }
  ]
}
\`\`\`

### 信息不足情况
\`\`\`json
{
  "status": "need_info",
  "required_info": ["需要补充的信息1", "需要补充的信息2"],
  "message": "详细说明需要什么信息"
}
\`\`\`

## 重要提醒
1. 页面快照是结构化文本，不是图像
2. 使用ref属性精确定位元素，如[ref=e685]
3. 严格按照system_guide.md的操作流程
4. 对于下架任务，必须包含完整的导航和操作步骤
5. 只返回JSON格式，不要包含markdown标记或其他文字

请分析工单并生成相应的MCP任务。`;
    }

    /**
     * 解析AI响应为MCP任务 - 基于测试成功的解析逻辑
     */
    parseMCPResponse(aiResponse, ticket) {
        try {
            logger.info('🔧 开始解析AI响应...');
            logger.info(`📄 原始响应长度: ${aiResponse.length}`);
            logger.info(`📋 原始响应内容: ${aiResponse.substring(0, 200)}...`);

            // 清理AI响应，移除markdown标记
            let cleanResponse = aiResponse.trim();

            // 移除各种可能的markdown标记
            if (cleanResponse.startsWith('```json')) {
                cleanResponse = cleanResponse.replace(/```json\n?/, '').replace(/\n?```$/, '');
            }
            if (cleanResponse.startsWith('```')) {
                cleanResponse = cleanResponse.replace(/```\n?/, '').replace(/\n?```$/, '');
            }

            // 移除可能的前后文字
            const jsonStart = cleanResponse.indexOf('{');
            const jsonEnd = cleanResponse.lastIndexOf('}');
            if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
                cleanResponse = cleanResponse.substring(jsonStart, jsonEnd + 1);
            }

            logger.info(`🔧 清理后的响应: ${cleanResponse}`);

            const parsed = JSON.parse(cleanResponse);
            logger.info('✅ JSON解析成功');

            if (parsed.status === 'ready' && parsed.tasks && parsed.tasks.length > 0) {
                const task = parsed.tasks[0];
                task.id = task.id || `mcp_task_${ticket.id}_${Date.now()}`;
                task.ticketId = ticket.id;

                logger.info(`✅ 任务解析成功: ${task.id}, 操作数量: ${task.data?.actions?.length || 0}`);
                return task;
            } else if (parsed.status === 'need_info') {
                throw new Error(`需要补充信息: ${parsed.required_info?.join(', ')}`);
            } else {
                throw new Error('AI响应格式不正确');
            }

        } catch (error) {
            logger.error('❌ 解析AI响应失败:', error);
            logger.error('❌ 失败的响应内容:', aiResponse);
            throw new Error(`AI响应解析失败: ${error.message}`);
        }
    }

    /**
     * 创建回退MCP任务
     */
    createFallbackMCPTask(ticket) {
        logger.info('🔄 创建回退MCP任务...');

        return {
            id: `fallback_mcp_task_${ticket.id}_${Date.now()}`,
            type: 'mcp_browser_automation',
            description: `回退MCP任务: ${ticket.title}`,
            priority: 1,
            ticketId: ticket.id,
            data: {
                url: 'https://uat-merchant.aomiapp.com/#/bdlogin',
                actions: [
                    {
                        type: 'mcp_navigate',
                        url: 'https://uat-merchant.aomiapp.com/#/bdlogin',
                        description: '导航到BD商户后台'
                    },
                    {
                        type: 'mcp_snapshot',
                        description: '获取页面快照分析页面结构'
                    },
                    {
                        type: 'mcp_screenshot',
                        description: '截图记录当前页面状态'
                    }
                ]
            }
        };
    }

    /**
     * 加载系统指南
     */
    async loadSystemGuide() {
        try {
            const fs = require('fs').promises;
            const path = require('path');
            const guidePath = path.join(__dirname, '../../..', 'system_guide.md');
            return await fs.readFile(guidePath, 'utf8');
        } catch (error) {
            logger.warn('⚠️ 无法加载系统指南，使用默认指引');
            return '请按照标准流程执行操作';
        }
    }

    /**
     * 记录执行步骤
     */
    addExecutionStep(step) {
        const stepRecord = {
            id: `step_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            timestamp: new Date().toISOString(),
            ...step
        };

        this.executionSteps.unshift(stepRecord); // 最新的在前面

        // 保持历史记录在限制范围内
        if (this.executionSteps.length > this.maxStepsHistory) {
            this.executionSteps = this.executionSteps.slice(0, this.maxStepsHistory);
        }

        logger.debug(`📝 记录执行步骤: ${step.action || step.title}`);

        // 实时推送执行步骤到前端
        this.broadcastExecutionStep(stepRecord);
    }

    /**
     * 广播执行步骤到前端
     */
    broadcastExecutionStep(stepRecord) {
        try {
            // 如果有WebSocket连接，推送执行步骤更新
            if (this.wsConnection && this.wsConnection.readyState === 1) { // WebSocket.OPEN = 1
                const message = {
                    type: 'execution_step_added',
                    data: {
                        id: stepRecord.id,
                        title: stepRecord.title || stepRecord.action || '执行步骤',
                        description: stepRecord.description || stepRecord.details || '',
                        status: stepRecord.status || 'completed',
                        timestamp: stepRecord.timestamp,
                        ticketId: stepRecord.ticketId,
                        iteration: stepRecord.iteration,
                        tool: stepRecord.tool,
                        element: stepRecord.element
                    },
                    timestamp: new Date().toISOString()
                };

                this.wsConnection.send(JSON.stringify(message));
                logger.debug(`📡 实时推送执行步骤: ${stepRecord.title || stepRecord.action}`);
            } else {
                logger.debug(`⚠️ WebSocket连接不可用，跳过实时推送`);
            }
        } catch (error) {
            logger.warn(`⚠️ 推送执行步骤失败: ${error.message}`);
        }
    }

    /**
     * 设置WebSocket连接（由AI助手服务调用）
     */
    setWebSocketConnection(wsConnection) {
        this.wsConnection = wsConnection;
        logger.info(`🔗 TaskProcessor已连接WebSocket`);
    }

    /**
     * 获取最近的执行步骤
     */
    getRecentExecutionSteps(limit = 10) {
        return this.executionSteps.slice(0, limit).map(step => ({
            id: step.id,
            title: step.title || step.action || '执行步骤',
            description: step.description || step.details || '',
            status: step.status || 'completed',
            timestamp: step.timestamp,
            ticketId: step.ticketId,
            iteration: step.iteration,
            tool: step.tool,
            element: step.element
        }));
    }

    /**
     * 清空执行步骤历史
     */
    clearExecutionSteps() {
        this.executionSteps = [];
        logger.info('🧹 已清空执行步骤历史');
    }
}

module.exports = TaskProcessor;
