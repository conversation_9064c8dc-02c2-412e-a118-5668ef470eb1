const axios = require('axios');
const logger = require('./utils/logger');

class TaskParser {
    constructor() {
        this.qwenApiKey = process.env.QWEN_API_KEY;
        this.qwenBaseUrl = process.env.QWEN_BASE_URL;
    }

    async parseTicket(ticket) {
        logger.info(`解析工单内容: ${ticket.title}`);
        
        // 简单的关键词匹配解析，避免复杂的AI推理
        const content = ticket.content.toLowerCase();
        const tasks = [];
        
        // 检测百度搜索任务
        if (content.includes('百度') && content.includes('搜索')) {
            const searchKeyword = this.extractSearchKeyword(ticket.content);
            tasks.push({
                type: 'browser_automation',
                description: `在百度搜索"${searchKeyword}"`,
                actions: [
                    { type: 'navigate', url: 'https://www.baidu.com' },
                    { type: 'wait', selector: '#kw', timeout: 5000 },
                    { type: 'input', selector: '#kw', value: searchKeyword },
                    { type: 'click', selector: '#su' },
                    { type: 'wait', selector: '.result', timeout: 10000 },
                    { type: 'screenshot', description: '搜索结果页面截图' }
                ]
            });
        }
        
        // 检测商品下架任务
        else if (content.includes('下架') && (content.includes('商品') || content.includes('小泉居'))) {
            const storeName = this.extractStoreName(ticket.content);
            const productName = this.extractProductName(ticket.content);
            
            tasks.push({
                type: 'browser_automation',
                description: `下架${storeName}的${productName}商品`,
                actions: [
                    { type: 'navigate', url: 'https://uat-merchant.aomiapp.com/#/bdlogin' },
                    { type: 'wait', selector: '.login-form', timeout: 10000 },
                    { type: 'manual_login', description: '等待用户扫码登录' },
                    { type: 'navigate', url: 'https://uat-merchant.aomiapp.com/#/store/list' },
                    { type: 'search_store', storeName: storeName },
                    { type: 'enter_store_management' },
                    { type: 'navigate_to_products' },
                    { type: 'search_product', productName: productName },
                    { type: 'delist_product' },
                    { type: 'screenshot', description: '商品下架完成截图' }
                ]
            });
        }
        
        // 默认任务：如果无法识别，创建一个通用的浏览器任务
        else {
            tasks.push({
                type: 'browser_automation',
                description: '执行通用浏览器自动化任务',
                actions: [
                    { type: 'screenshot', description: '任务开始截图' },
                    { type: 'manual_review', description: '需要人工审核任务内容' }
                ]
            });
        }
        
        return tasks;
    }

    extractSearchKeyword(content) {
        // 提取搜索关键词
        const matches = content.match(/搜索[关键词]*[""]([^"""]+)[""]/) || 
                       content.match(/搜索[关键词]*[：:]\s*([^\s\n]+)/) ||
                       content.match(/[""]([^"""]*RPA[^"""]*)[""]/);
        
        if (matches && matches[1]) {
            return matches[1].trim();
        }
        
        // 默认搜索词
        return 'RPA自动化';
    }

    extractStoreName(content) {
        // 提取门店名称
        const matches = content.match(/小泉居[（(]([^）)]+)[）)]/) ||
                       content.match(/([^，,\s]+店)/) ||
                       content.match(/门店[：:]?\s*([^\s\n]+)/);
        
        if (matches && matches[1]) {
            return matches[1].trim();
        }
        
        return '威翠店';
    }

    extractProductName(content) {
        // 提取商品名称
        const matches = content.match(/的([^的\s]+商品)/) ||
                       content.match(/商品[：:]?\s*([^\s\n]+)/) ||
                       content.match(/([^，,\s]*[饭面粥][^，,\s]*)/);
        
        if (matches && matches[1]) {
            return matches[1].replace('商品', '').trim();
        }
        
        return '咖哩焗魚飯';
    }
}

module.exports = TaskParser;
