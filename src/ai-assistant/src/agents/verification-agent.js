/**
 * 验证Agent - 负责截图分析和任务完成度验证
 * 专注于通过页面内容分析验证任务是否真正完成
 */

class VerificationAgent {
    constructor(modelClient, logger) {
        this.modelClient = modelClient;
        this.logger = logger;
        this.agentName = '验证Agent';
    }

    /**
     * 验证任务完成度 - 核心功能
     */
    async verifyTaskCompletion(plan, currentPageState, screenshots) {
        try {
            this.logger.info(`🔍 ${this.agentName}开始验证任务完成度...`);
            
            // 提取页面关键信息
            const pageAnalysis = await this.analyzePageContent(currentPageState, plan);
            
            // 分析截图（如果有视觉模型）
            const screenshotAnalysis = await this.analyzeScreenshots(screenshots, plan);
            
            // 综合验证
            const verificationResult = await this.performComprehensiveVerification(
                plan, pageAnalysis, screenshotAnalysis
            );
            
            this.logger.info(`✅ ${this.agentName}验证完成`);
            this.logger.info(`📊 验证结果: ${verificationResult.isCompleted ? '✅ 已完成' : '❌ 未完成'}`);
            this.logger.info(`📈 置信度: ${verificationResult.confidence}%`);
            
            return verificationResult;
            
        } catch (error) {
            this.logger.error(`❌ ${this.agentName}验证失败:`, error);
            return this.generateFailureVerification(error);
        }
    }

    /**
     * 分析页面内容
     */
    async analyzePageContent(pageState, plan) {
        try {
            this.logger.info(`📄 ${this.agentName}分析页面内容...`);
            
            const prompt = `
你是专业的页面内容分析专家。请分析当前页面状态，判断是否包含任务完成的证据。

任务计划：
- 任务类型: ${plan.taskType}
- 目标门店: ${plan.targetStore || '未指定'}
- 目标商品: ${plan.targetProduct || '未指定'}
- 预期操作: ${plan.expectedAction}
- 成功标准: ${plan.successCriteria.join(', ')}

当前页面状态：
- URL: ${pageState.url}
- 标题: ${pageState.title}
- 按钮数量: ${pageState.buttons?.length || 0}
- 输入框数量: ${pageState.inputs?.length || 0}
- 链接数量: ${pageState.links?.length || 0}

页面元素详情：
${this.formatPageElements(pageState)}

请分析页面内容并返回JSON格式结果：
{
    "pageType": "登录页|管理后台|商品列表|搜索结果|确认页面|其他",
    "relevantElements": [
        {
            "element": "元素描述",
            "relevance": "与任务的相关性",
            "evidence": "提供的证据"
        }
    ],
    "taskEvidence": {
        "storeFound": true/false,
        "productFound": true/false,
        "actionCompleted": true/false,
        "statusVerified": true/false
    },
    "contentKeywords": ["页面中的关键词"],
    "missingElements": ["缺失的关键元素"],
    "nextStepSuggestion": "建议的下一步操作"
}

重要：请只返回纯JSON格式。`;

            const response = await this.modelClient.callMainModel(prompt);
            const cleanedResponse = this.cleanJsonResponse(response);
            const analysis = JSON.parse(cleanedResponse);
            
            this.logger.info(`✅ ${this.agentName}页面内容分析完成`);
            this.logger.info(`📋 页面类型: ${analysis.pageType}`);
            
            return analysis;
            
        } catch (error) {
            this.logger.error(`❌ ${this.agentName}页面内容分析失败:`, error);
            return {
                pageType: "未知",
                relevantElements: [],
                taskEvidence: {
                    storeFound: false,
                    productFound: false,
                    actionCompleted: false,
                    statusVerified: false
                },
                contentKeywords: [],
                missingElements: ["分析失败"],
                nextStepSuggestion: "重新分析页面"
            };
        }
    }

    /**
     * 分析截图（如果有视觉模型）
     */
    async analyzeScreenshots(screenshots, plan) {
        try {
            if (!screenshots || screenshots.length === 0) {
                this.logger.info(`📸 ${this.agentName}无截图可分析`);
                return {
                    hasScreenshots: false,
                    visualEvidence: [],
                    confidence: 0
                };
            }
            
            this.logger.info(`📸 ${this.agentName}分析 ${screenshots.length} 张截图...`);
            
            // 这里可以集成视觉模型分析截图
            // 目前返回基础分析结果
            
            return {
                hasScreenshots: true,
                visualEvidence: [
                    {
                        screenshot: "最新截图",
                        findings: ["页面已加载", "界面可见"],
                        relevance: "中等"
                    }
                ],
                confidence: 50
            };
            
        } catch (error) {
            this.logger.error(`❌ ${this.agentName}截图分析失败:`, error);
            return {
                hasScreenshots: false,
                visualEvidence: [],
                confidence: 0
            };
        }
    }

    /**
     * 执行综合验证
     */
    async performComprehensiveVerification(plan, pageAnalysis, screenshotAnalysis) {
        try {
            this.logger.info(`🎯 ${this.agentName}执行综合验证...`);
            
            const prompt = `
你是RPA任务验证专家。请根据任务计划、页面分析和截图分析，综合判断任务是否真正完成。

任务计划：
${JSON.stringify(plan, null, 2)}

页面分析：
${JSON.stringify(pageAnalysis, null, 2)}

截图分析：
${JSON.stringify(screenshotAnalysis, null, 2)}

请进行严格的验证，特别关注：
1. 目标门店是否正确识别
2. 目标商品是否找到
3. 预期操作是否真正执行
4. 操作结果是否符合预期

返回详细的验证结果（JSON格式）：
{
    "isCompleted": true/false,
    "confidence": 0-100,
    "verificationDetails": {
        "storeVerification": {
            "found": true/false,
            "correctStore": true/false,
            "evidence": "验证证据"
        },
        "productVerification": {
            "found": true/false,
            "correctProduct": true/false,
            "statusVerified": true/false,
            "evidence": "验证证据"
        },
        "actionVerification": {
            "actionExecuted": true/false,
            "resultVisible": true/false,
            "evidence": "验证证据"
        }
    },
    "successCriteriaCheck": [
        {
            "criteria": "成功标准",
            "met": true/false,
            "evidence": "证据"
        }
    ],
    "evidenceSummary": {
        "positiveEvidence": ["支持完成的证据"],
        "negativeEvidence": ["反对完成的证据"],
        "missingEvidence": ["缺失的关键证据"]
    },
    "finalAssessment": "最终评估结论",
    "recommendedActions": ["建议的后续操作"]
}

重要：请严格验证，宁可保守也不要误判。只返回纯JSON格式。`;

            const response = await this.modelClient.callMainModel(prompt);
            const cleanedResponse = this.cleanJsonResponse(response);
            const verification = JSON.parse(cleanedResponse);
            
            this.logger.info(`✅ ${this.agentName}综合验证完成`);
            
            return verification;
            
        } catch (error) {
            this.logger.error(`❌ ${this.agentName}综合验证失败:`, error);
            return this.generateFailureVerification(error);
        }
    }

    /**
     * 验证特定商品状态
     */
    async verifyProductStatus(storeName, productName, expectedStatus, pageState) {
        try {
            this.logger.info(`🔍 ${this.agentName}验证商品状态: ${storeName} - ${productName}`);
            
            const prompt = `
你是商品状态验证专家。请在当前页面中查找指定商品的状态。

查找目标：
- 门店名称: ${storeName}
- 商品名称: ${productName}
- 预期状态: ${expectedStatus}

当前页面状态：
${JSON.stringify(pageState, null, 2)}

请仔细查找并返回验证结果（JSON格式）：
{
    "productFound": true/false,
    "storeMatched": true/false,
    "currentStatus": "当前状态",
    "statusMatched": true/false,
    "evidence": "找到的证据",
    "location": "在页面中的位置",
    "confidence": 0-100
}

重要：请只返回纯JSON格式。`;

            const response = await this.modelClient.callMainModel(prompt);
            const cleanedResponse = this.cleanJsonResponse(response);
            const verification = JSON.parse(cleanedResponse);
            
            this.logger.info(`✅ ${this.agentName}商品状态验证完成`);
            this.logger.info(`📦 商品找到: ${verification.productFound ? '是' : '否'}`);
            this.logger.info(`🏪 门店匹配: ${verification.storeMatched ? '是' : '否'}`);
            this.logger.info(`📊 状态匹配: ${verification.statusMatched ? '是' : '否'}`);
            
            return verification;
            
        } catch (error) {
            this.logger.error(`❌ ${this.agentName}商品状态验证失败:`, error);
            return {
                productFound: false,
                storeMatched: false,
                currentStatus: "未知",
                statusMatched: false,
                evidence: `验证失败: ${error.message}`,
                location: "未找到",
                confidence: 0
            };
        }
    }

    /**
     * 格式化页面元素信息
     */
    formatPageElements(pageState) {
        let formatted = '';
        
        if (pageState.buttons && pageState.buttons.length > 0) {
            formatted += '按钮: ' + pageState.buttons.map(btn => btn.text).join(', ') + '\n';
        }
        
        if (pageState.inputs && pageState.inputs.length > 0) {
            formatted += '输入框: ' + pageState.inputs.map(input => 
                `${input.placeholder || input.name || input.id || '未命名'}`
            ).join(', ') + '\n';
        }
        
        if (pageState.links && pageState.links.length > 0) {
            formatted += '链接: ' + pageState.links.slice(0, 10).map(link => link.text).join(', ') + '\n';
        }
        
        return formatted || '无可用元素信息';
    }

    /**
     * 生成失败验证结果
     */
    generateFailureVerification(error) {
        return {
            isCompleted: false,
            confidence: 0,
            verificationDetails: {
                storeVerification: {
                    found: false,
                    correctStore: false,
                    evidence: "验证过程失败"
                },
                productVerification: {
                    found: false,
                    correctProduct: false,
                    statusVerified: false,
                    evidence: "验证过程失败"
                },
                actionVerification: {
                    actionExecuted: false,
                    resultVisible: false,
                    evidence: "验证过程失败"
                }
            },
            successCriteriaCheck: [],
            evidenceSummary: {
                positiveEvidence: [],
                negativeEvidence: [`验证失败: ${error.message}`],
                missingEvidence: ["所有验证数据"]
            },
            finalAssessment: `验证Agent执行失败: ${error.message}`,
            recommendedActions: ["重新执行验证", "人工检查", "系统诊断"]
        };
    }

    /**
     * 清理JSON响应
     */
    cleanJsonResponse(response) {
        try {
            let cleaned = response.trim();
            cleaned = cleaned.replace(/^```(?:json)?\s*\n?/i, '');
            cleaned = cleaned.replace(/\n?\s*```\s*$/i, '');
            const jsonMatch = cleaned.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                cleaned = jsonMatch[0];
            }
            return cleaned.replace(/\n\s*\n/g, '\n').trim();
        } catch (error) {
            this.logger.warn('⚠️ 清理JSON响应失败，使用原始响应:', error);
            return response;
        }
    }
}

module.exports = VerificationAgent;
