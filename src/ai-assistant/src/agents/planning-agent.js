/**
 * 规划Agent - 负责任务分析和执行计划制定
 * 专注于理解任务目标，制定详细的执行步骤
 */

class PlanningAgent {
    constructor(modelClient, logger) {
        this.modelClient = modelClient;
        this.logger = logger;
        this.agentName = '规划Agent';
    }

    /**
     * 分析任务并制定执行计划
     */
    async analyzeTaskAndCreatePlan(task) {
        try {
            this.logger.info(`📋 ${this.agentName}开始分析任务...`);
            
            const prompt = `
你是一个专业的RPA任务规划专家。请仔细分析以下任务，制定详细的执行计划。

任务信息：
- 标题: ${task.title || task.description}
- 内容: ${task.originalContent || task.content || ''}
- 目标: ${task.objective || '完成指定的RPA任务'}

请分析任务并返回JSON格式的执行计划：
{
    "taskType": "商品下架|商品上架|信息更新|搜索查询|其他",
    "targetStore": "目标门店名称（如果有）",
    "targetProduct": "目标商品名称（如果有）",
    "expectedAction": "预期操作（下架/上架/更新/查询等）",
    "successCriteria": [
        "成功标准1：具体的验证条件",
        "成功标准2：具体的验证条件"
    ],
    "executionSteps": [
        {
            "stepNumber": 1,
            "action": "navigate|login|search|click|verify",
            "description": "步骤描述",
            "target": "操作目标",
            "expectedResult": "预期结果",
            "verificationMethod": "如何验证这一步是否成功"
        }
    ],
    "riskPoints": [
        "可能遇到的问题1",
        "可能遇到的问题2"
    ],
    "fallbackStrategy": "如果主要流程失败的备用策略"
}

重要：请只返回纯JSON格式，不要包含markdown标记。`;

            const response = await this.modelClient.callMainModel(prompt);
            const cleanedResponse = this.cleanJsonResponse(response);
            const plan = JSON.parse(cleanedResponse);
            
            this.logger.info(`✅ ${this.agentName}任务分析完成`);
            this.logger.info(`📊 任务类型: ${plan.taskType}`);
            this.logger.info(`🎯 目标门店: ${plan.targetStore || '未指定'}`);
            this.logger.info(`📦 目标商品: ${plan.targetProduct || '未指定'}`);
            this.logger.info(`📝 执行步骤: ${plan.executionSteps.length}个`);
            
            return plan;
            
        } catch (error) {
            this.logger.error(`❌ ${this.agentName}任务分析失败:`, error);
            
            // 返回默认计划
            return {
                taskType: "其他",
                targetStore: null,
                targetProduct: null,
                expectedAction: "执行基础操作",
                successCriteria: ["完成基本操作流程"],
                executionSteps: [
                    {
                        stepNumber: 1,
                        action: "navigate",
                        description: "导航到目标页面",
                        target: "管理后台",
                        expectedResult: "成功打开页面",
                        verificationMethod: "检查页面标题和URL"
                    }
                ],
                riskPoints: ["网络连接问题", "页面加载失败"],
                fallbackStrategy: "重试操作或请求人工协助"
            };
        }
    }

    /**
     * 根据当前状态调整执行计划
     */
    async adjustPlan(originalPlan, currentState, issues) {
        try {
            this.logger.info(`🔄 ${this.agentName}根据当前状态调整计划...`);
            
            const prompt = `
你是RPA任务规划专家。根据当前执行状态和遇到的问题，调整执行计划。

原始计划：
${JSON.stringify(originalPlan, null, 2)}

当前状态：
- 当前页面: ${currentState.url || '未知'}
- 页面标题: ${currentState.title || '未知'}
- 已完成步骤: ${currentState.completedSteps || 0}

遇到的问题：
${issues.map(issue => `- ${issue}`).join('\n')}

请返回调整后的执行计划（JSON格式）：
{
    "adjustmentReason": "调整原因",
    "modifiedSteps": [
        {
            "stepNumber": 1,
            "action": "操作类型",
            "description": "调整后的步骤描述",
            "target": "操作目标",
            "expectedResult": "预期结果",
            "verificationMethod": "验证方法"
        }
    ],
    "newSuccessCriteria": ["新的成功标准"],
    "estimatedCompletion": "预计完成时间"
}

重要：请只返回纯JSON格式。`;

            const response = await this.modelClient.callMainModel(prompt);
            const cleanedResponse = this.cleanJsonResponse(response);
            const adjustedPlan = JSON.parse(cleanedResponse);
            
            this.logger.info(`✅ ${this.agentName}计划调整完成: ${adjustedPlan.adjustmentReason}`);
            
            return adjustedPlan;
            
        } catch (error) {
            this.logger.error(`❌ ${this.agentName}计划调整失败:`, error);
            return null;
        }
    }

    /**
     * 评估任务完成度
     */
    async evaluateTaskCompletion(plan, executionResults, finalState) {
        try {
            this.logger.info(`📊 ${this.agentName}评估任务完成度...`);
            
            const prompt = `
你是RPA任务评估专家。根据执行计划、执行结果和最终状态，评估任务是否真正完成。

执行计划：
${JSON.stringify(plan, null, 2)}

执行结果：
${JSON.stringify(executionResults, null, 2)}

最终状态：
- 页面URL: ${finalState.url || '未知'}
- 页面标题: ${finalState.title || '未知'}
- 页面内容关键词: ${finalState.keywords || '未提取'}

请返回评估结果（JSON格式）：
{
    "isCompleted": true/false,
    "completionPercentage": 0-100,
    "achievedCriteria": ["已达成的成功标准"],
    "missedCriteria": ["未达成的成功标准"],
    "evidenceFound": ["找到的完成证据"],
    "evidenceMissing": ["缺失的完成证据"],
    "finalAssessment": "最终评估结论",
    "recommendedActions": ["建议的后续操作"]
}

重要：请只返回纯JSON格式。`;

            const response = await this.modelClient.callMainModel(prompt);
            const cleanedResponse = this.cleanJsonResponse(response);
            const evaluation = JSON.parse(cleanedResponse);
            
            this.logger.info(`✅ ${this.agentName}任务评估完成`);
            this.logger.info(`📊 完成度: ${evaluation.completionPercentage}%`);
            this.logger.info(`🎯 任务状态: ${evaluation.isCompleted ? '✅ 已完成' : '❌ 未完成'}`);
            
            return evaluation;
            
        } catch (error) {
            this.logger.error(`❌ ${this.agentName}任务评估失败:`, error);
            return {
                isCompleted: false,
                completionPercentage: 0,
                achievedCriteria: [],
                missedCriteria: ["评估过程失败"],
                evidenceFound: [],
                evidenceMissing: ["无法获取评估数据"],
                finalAssessment: "评估失败，无法确定任务完成状态",
                recommendedActions: ["重新执行任务", "人工检查"]
            };
        }
    }

    /**
     * 清理JSON响应
     */
    cleanJsonResponse(response) {
        try {
            let cleaned = response.trim();
            cleaned = cleaned.replace(/^```(?:json)?\s*\n?/i, '');
            cleaned = cleaned.replace(/\n?\s*```\s*$/i, '');
            const jsonMatch = cleaned.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                cleaned = jsonMatch[0];
            }
            return cleaned.replace(/\n\s*\n/g, '\n').trim();
        } catch (error) {
            this.logger.warn('⚠️ 清理JSON响应失败，使用原始响应:', error);
            return response;
        }
    }
}

module.exports = PlanningAgent;
