/**
 * 协调Agent - 统筹多Agent协作的核心控制器
 * 负责Agent间的协调、任务分配和整体流程控制
 */

const PlanningAgent = require('./planning-agent');
const VerificationAgent = require('./verification-agent');
const ReportingAgent = require('./reporting-agent');

class CoordinatorAgent {
    constructor(modelClient, logger, workorderApiUrl) {
        this.modelClient = modelClient;
        this.logger = logger;
        this.workorderApiUrl = workorderApiUrl;
        this.agentName = '协调Agent';
        
        // 初始化各个专业Agent
        this.planningAgent = new PlanningAgent(modelClient, logger);
        this.verificationAgent = new VerificationAgent(modelClient, logger);
        this.reportingAgent = new ReportingAgent(modelClient, logger, workorderApiUrl);
        
        // 执行状态
        this.currentTask = null;
        this.executionPlan = null;
        this.executionResults = [];
        this.verificationResults = null;
        this.finalReport = null;
    }

    /**
     * 执行完整的多Agent RPA任务
     */
    async executeMultiAgentTask(task, executionEngine) {
        try {
            this.logger.info(`🎯 ${this.agentName}开始协调多Agent任务执行...`);
            this.currentTask = task;
            
            // 阶段1: 任务规划
            this.logger.info(`📋 阶段1: 任务规划`);
            this.executionPlan = await this.planningAgent.analyzeTaskAndCreatePlan(task);
            
            // 发送规划完成的实时更新
            await this.reportingAgent.sendRealtimeUpdate(
                task.ticketId, 
                'progress', 
                '任务规划完成，开始执行操作',
                { plan: this.executionPlan }
            );
            
            // 阶段2: 任务执行
            this.logger.info(`🎬 阶段2: 任务执行`);
            this.executionResults = await this.executeTaskWithMonitoring(executionEngine);
            
            // 阶段3: 结果验证
            this.logger.info(`🔍 阶段3: 结果验证`);
            this.verificationResults = await this.performTaskVerification(executionEngine);
            
            // 阶段4: 报告生成和同步
            this.logger.info(`📝 阶段4: 报告生成`);
            this.finalReport = await this.generateAndSyncReport();
            
            // 返回最终结果
            const finalResult = {
                success: this.verificationResults.isCompleted,
                confidence: this.verificationResults.confidence,
                executionPlan: this.executionPlan,
                executionResults: this.executionResults,
                verificationResults: this.verificationResults,
                finalReport: this.finalReport,
                stepsCompleted: this.executionResults.length
            };
            
            this.logger.info(`✅ ${this.agentName}多Agent任务协调完成`);
            this.logger.info(`📊 最终结果: ${finalResult.success ? '✅ 成功' : '❌ 失败'}`);
            this.logger.info(`📈 置信度: ${finalResult.confidence}%`);
            
            return finalResult;
            
        } catch (error) {
            this.logger.error(`❌ ${this.agentName}任务协调失败:`, error);
            
            // 生成失败报告
            const failureReport = await this.handleExecutionFailure(error);
            
            return {
                success: false,
                confidence: 0,
                error: error.message,
                executionPlan: this.executionPlan,
                executionResults: this.executionResults,
                verificationResults: null,
                finalReport: failureReport,
                stepsCompleted: this.executionResults.length
            };
        }
    }

    /**
     * 带监控的任务执行
     */
    async executeTaskWithMonitoring(executionEngine) {
        const results = [];
        const maxSteps = 20;
        let currentStep = 0;
        
        try {
            // 执行初始操作（如果有）
            if (this.executionPlan.executionSteps && this.executionPlan.executionSteps.length > 0) {
                const initialStep = this.executionPlan.executionSteps[0];

                this.logger.info(`🎬 执行初始步骤: ${initialStep.description}`);

                // 转换为执行引擎可识别的格式
                const actionStep = this.convertPlanStepToAction(initialStep);

                const stepResult = await this.executeStepWithVerification(
                    actionStep, executionEngine, currentStep + 1
                );

                results.push(stepResult);
                currentStep++;
            }
            
            // 执行后续步骤
            while (currentStep < maxSteps) {
                // 获取当前页面状态 - 修复接口调用
                const currentState = await this.getCurrentPageState(executionEngine);
                
                // 检查是否需要人工干预（如登录）
                const interventionNeeded = await this.checkForHumanIntervention(currentState);
                if (interventionNeeded.required) {
                    this.logger.info(`⏸️ 需要人工干预: ${interventionNeeded.reason}`);
                    
                    // 发送人工干预请求
                    await this.reportingAgent.sendRealtimeUpdate(
                        this.currentTask.ticketId,
                        'intervention',
                        interventionNeeded.message,
                        { state: currentState }
                    );
                    
                    // 等待人工干预完成的信号
                    await this.waitForHumanIntervention(interventionNeeded);
                    continue;
                }
                
                // 检查任务是否已完成
                const completionCheck = await this.verificationAgent.verifyTaskCompletion(
                    this.executionPlan, currentState, []
                );
                
                if (completionCheck.isCompleted && completionCheck.confidence > 80) {
                    this.logger.info(`🎉 任务提前完成，置信度: ${completionCheck.confidence}%`);
                    break;
                }
                
                // 规划下一步操作
                const nextAction = await this.planNextAction(currentState, results);
                if (!nextAction) {
                    this.logger.info(`⏹️ 无法规划下一步操作，结束执行`);
                    break;
                }
                
                // 执行下一步
                const stepResult = await this.executeStepWithVerification(
                    nextAction, executionEngine, currentStep + 1
                );
                
                results.push(stepResult);
                currentStep++;
                
                // 发送进度更新
                await this.reportingAgent.sendRealtimeUpdate(
                    this.currentTask.ticketId,
                    'progress',
                    `完成第${currentStep}步: ${nextAction.description}`,
                    { step: currentStep, total: maxSteps }
                );
            }
            
            return results;
            
        } catch (error) {
            this.logger.error(`❌ ${this.agentName}执行监控失败:`, error);
            throw error;
        }
    }

    /**
     * 带验证的步骤执行
     */
    async executeStepWithVerification(step, executionEngine, stepNumber) {
        try {
            this.logger.info(`🎬 执行步骤 ${stepNumber}: ${step.description}`);
            
            // 执行操作 - 修复接口调用
            const executionResult = await this.executeAction(executionEngine, step);

            // 获取执行后的页面状态
            const postState = await this.getCurrentPageState(executionEngine);
            
            // 验证步骤结果
            const stepVerification = await this.verifyStepCompletion(
                step, executionResult, postState
            );
            
            const stepResult = {
                step: stepNumber,
                action: step,
                executionResult: executionResult,
                verification: stepVerification,
                pageState: postState,
                timestamp: new Date().toISOString()
            };
            
            this.logger.info(`${stepVerification.success ? '✅' : '❌'} 步骤 ${stepNumber} ${stepVerification.success ? '成功' : '失败'}`);
            
            return stepResult;
            
        } catch (error) {
            this.logger.error(`❌ 步骤 ${stepNumber} 执行失败:`, error);
            
            return {
                step: stepNumber,
                action: step,
                executionResult: { success: false, error: error.message },
                verification: { success: false, reason: `执行异常: ${error.message}` },
                pageState: null,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * 检查是否需要人工干预
     */
    async checkForHumanIntervention(pageState) {
        try {
            // 检查登录页面
            if (pageState.title && pageState.title.includes('登录')) {
                return {
                    required: true,
                    reason: '需要登录',
                    message: '检测到登录页面，请完成登录后继续',
                    type: 'login'
                };
            }
            
            // 检查验证码
            if (pageState.inputs && pageState.inputs.some(input => 
                input.placeholder && input.placeholder.includes('验证码'))) {
                return {
                    required: true,
                    reason: '需要验证码',
                    message: '检测到验证码输入，请完成验证后继续',
                    type: 'captcha'
                };
            }
            
            // 检查错误页面
            if (pageState.title && (pageState.title.includes('错误') || pageState.title.includes('404'))) {
                return {
                    required: true,
                    reason: '页面错误',
                    message: '检测到错误页面，需要人工处理',
                    type: 'error'
                };
            }
            
            return { required: false };
            
        } catch (error) {
            this.logger.error(`❌ 人工干预检查失败:`, error);
            return { required: false };
        }
    }

    /**
     * 等待人工干预完成
     */
    async waitForHumanIntervention(intervention) {
        // 这里可以实现等待机制
        // 例如：轮询页面状态变化，或等待WebSocket信号
        this.logger.info(`⏳ 等待人工干预完成: ${intervention.reason}`);
        
        // 简单的等待实现
        await new Promise(resolve => setTimeout(resolve, 30000)); // 等待30秒
    }

    /**
     * 规划下一步操作
     */
    async planNextAction(currentState, executionHistory) {
        try {
            // 基于当前状态和执行历史规划下一步
            const issues = executionHistory
                .filter(result => !result.verification.success)
                .map(result => result.verification.reason);
            
            if (issues.length > 0) {
                // 如果有问题，调整计划
                const adjustedPlan = await this.planningAgent.adjustPlan(
                    this.executionPlan, currentState, issues
                );
                
                if (adjustedPlan && adjustedPlan.modifiedSteps.length > 0) {
                    return adjustedPlan.modifiedSteps[0];
                }
            }
            
            // 否则继续原计划
            const nextStepIndex = executionHistory.length;
            if (this.executionPlan.executionSteps && 
                nextStepIndex < this.executionPlan.executionSteps.length) {
                return this.executionPlan.executionSteps[nextStepIndex];
            }
            
            return null;
            
        } catch (error) {
            this.logger.error(`❌ 下一步规划失败:`, error);
            return null;
        }
    }

    /**
     * 验证步骤完成
     */
    async verifyStepCompletion(step, executionResult, pageState) {
        try {
            // 基础验证：操作是否成功执行
            if (!executionResult.success) {
                return {
                    success: false,
                    reason: `操作执行失败: ${executionResult.error || '未知错误'}`
                };
            }
            
            // 页面状态验证
            if (step.expectedResult) {
                // 这里可以添加更详细的验证逻辑
                return {
                    success: true,
                    reason: '操作执行成功',
                    evidence: `页面状态: ${pageState.title}`
                };
            }
            
            return { success: true, reason: '步骤执行完成' };
            
        } catch (error) {
            return {
                success: false,
                reason: `验证失败: ${error.message}`
            };
        }
    }

    /**
     * 执行任务验证
     */
    async performTaskVerification(executionEngine) {
        try {
            const currentState = await this.getCurrentPageState(executionEngine);
            const screenshots = []; // 可以添加截图获取逻辑

            return await this.verificationAgent.verifyTaskCompletion(
                this.executionPlan, currentState, screenshots
            );

        } catch (error) {
            this.logger.error(`❌ 任务验证失败:`, error);
            return {
                isCompleted: false,
                confidence: 0,
                finalAssessment: `验证失败: ${error.message}`
            };
        }
    }

    /**
     * 生成并同步报告
     */
    async generateAndSyncReport() {
        try {
            const report = await this.reportingAgent.generateExecutionReport(
                this.currentTask,
                this.executionPlan,
                this.executionResults,
                this.verificationResults
            );
            
            await this.reportingAgent.syncReportToWorkorder(
                this.currentTask.ticketId,
                report
            );
            
            return report;
            
        } catch (error) {
            this.logger.error(`❌ 报告生成同步失败:`, error);
            throw error;
        }
    }

    /**
     * 转换规划步骤为执行动作
     */
    convertPlanStepToAction(planStep) {
        return {
            action_type: planStep.action || 'navigate',
            action_data: {
                url: planStep.target === '商品管理后台' ? 'https://www.baidu.com' : planStep.target,
                text: planStep.description
            },
            target_element: {
                selector: 'body',
                description: planStep.target || '页面主体'
            },
            reasoning: planStep.description || '执行规划步骤',
            expected_outcome: planStep.expectedResult || '完成操作'
        };
    }

    /**
     * 执行动作 - 适配执行引擎接口
     */
    async executeAction(executionEngine, action) {
        try {
            // 使用执行引擎的执行方法
            if (executionEngine.executeAction) {
                return await executionEngine.executeAction(action);
            } else if (executionEngine.executeStep) {
                return await executionEngine.executeStep(action);
            } else {
                // 基础执行逻辑
                this.logger.info(`🎬 执行基础操作: ${action.action_type}`);
                return {
                    success: true,
                    message: `已执行${action.action_type}操作`,
                    playwrightMethod: action.action_type
                };
            }
        } catch (error) {
            this.logger.error(`❌ 执行动作失败:`, error);
            return {
                success: false,
                error: error.message,
                message: `执行失败: ${error.message}`
            };
        }
    }

    /**
     * 获取当前页面状态 - 适配执行引擎接口
     */
    async getCurrentPageState(executionEngine) {
        try {
            // 使用执行引擎的页面分析方法
            if (executionEngine.analyzeCurrentPage) {
                return await executionEngine.analyzeCurrentPage();
            } else if (executionEngine.getCurrentState) {
                return await executionEngine.getCurrentState();
            } else {
                // 返回基础状态
                return {
                    url: 'unknown',
                    title: 'unknown',
                    buttons: [],
                    inputs: [],
                    links: []
                };
            }
        } catch (error) {
            this.logger.error(`❌ 获取页面状态失败:`, error);
            return {
                url: 'error',
                title: 'error',
                buttons: [],
                inputs: [],
                links: []
            };
        }
    }

    /**
     * 处理执行失败
     */
    async handleExecutionFailure(error) {
        try {
            const failureReport = {
                reportSummary: {
                    taskTitle: this.currentTask.title || this.currentTask.description,
                    executionStatus: '失败',
                    completionPercentage: 0,
                    finalResult: `执行失败: ${error.message}`
                },
                workorderUpdate: {
                    status: '处理失败',
                    notes: `多Agent协调执行失败: ${error.message}`,
                    reportSummary: '任务执行异常，请检查系统状态'
                }
            };
            
            await this.reportingAgent.syncReportToWorkorder(
                this.currentTask.ticketId,
                failureReport
            );
            
            return failureReport;
            
        } catch (reportError) {
            this.logger.error(`❌ 失败报告生成失败:`, reportError);
            return null;
        }
    }
}

module.exports = CoordinatorAgent;
