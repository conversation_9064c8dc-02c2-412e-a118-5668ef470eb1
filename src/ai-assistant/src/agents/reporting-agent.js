/**
 * 汇报Agent - 负责结果总结和工单状态同步
 * 专注于生成详细的执行报告，并同步到工单系统
 */

const axios = require('axios');

class ReportingAgent {
    constructor(modelClient, logger, workorderApiUrl) {
        this.modelClient = modelClient;
        this.logger = logger;
        this.workorderApiUrl = workorderApiUrl;
        this.agentName = '汇报Agent';
    }

    /**
     * 生成详细的执行报告
     */
    async generateExecutionReport(taskInfo, executionPlan, executionResults, verificationResults) {
        try {
            this.logger.info(`📝 ${this.agentName}开始生成执行报告...`);
            
            const prompt = `
你是专业的RPA执行报告生成专家。请根据任务信息、执行计划、执行结果和验证结果，生成一份详细的执行报告。

任务信息：
${JSON.stringify(taskInfo, null, 2)}

执行计划：
${JSON.stringify(executionPlan, null, 2)}

执行结果：
${JSON.stringify(executionResults, null, 2)}

验证结果：
${JSON.stringify(verificationResults, null, 2)}

请生成详细的执行报告（JSON格式）：
{
    "reportSummary": {
        "taskTitle": "任务标题",
        "executionStatus": "成功|失败|部分完成",
        "completionPercentage": 0-100,
        "executionTime": "执行耗时",
        "finalResult": "最终结果简述"
    },
    "taskAnalysis": {
        "originalObjective": "原始任务目标",
        "targetStore": "目标门店",
        "targetProduct": "目标商品",
        "expectedAction": "预期操作"
    },
    "executionDetails": {
        "totalSteps": 0,
        "completedSteps": 0,
        "failedSteps": 0,
        "keyOperations": [
            {
                "operation": "操作类型",
                "description": "操作描述",
                "result": "操作结果",
                "evidence": "操作证据"
            }
        ]
    },
    "verificationResults": {
        "verificationMethod": "验证方法",
        "evidenceFound": ["找到的证据"],
        "evidenceMissing": ["缺失的证据"],
        "finalVerification": "最终验证结论"
    },
    "businessImpact": {
        "storeAffected": "受影响的门店",
        "productAffected": "受影响的商品",
        "operationCompleted": "完成的操作",
        "businessResult": "业务结果"
    },
    "technicalDetails": {
        "pagesVisited": ["访问的页面"],
        "elementsInteracted": ["交互的元素"],
        "screenshotsTaken": 0,
        "errorsEncountered": ["遇到的错误"]
    },
    "recommendations": {
        "nextActions": ["建议的后续操作"],
        "improvements": ["改进建议"],
        "riskMitigation": ["风险缓解措施"]
    },
    "workorderUpdate": {
        "status": "已完成|处理失败|需要人工干预",
        "notes": "工单备注",
        "reportSummary": "报告摘要"
    }
}

重要：请只返回纯JSON格式，不要包含markdown标记。`;

            const response = await this.modelClient.callMainModel(prompt);
            const cleanedResponse = this.cleanJsonResponse(response);
            const report = JSON.parse(cleanedResponse);
            
            // 添加时间戳和元数据
            report.metadata = {
                generatedAt: new Date().toLocaleString('zh-CN'),
                agentVersion: 'v3.0',
                reportId: `RPT-${Date.now()}`,
                architecture: 'AI-First Multi-Agent RPA'
            };
            
            this.logger.info(`✅ ${this.agentName}执行报告生成完成`);
            this.logger.info(`📊 任务状态: ${report.reportSummary.executionStatus}`);
            this.logger.info(`📈 完成度: ${report.reportSummary.completionPercentage}%`);
            
            return report;
            
        } catch (error) {
            this.logger.error(`❌ ${this.agentName}报告生成失败:`, error);
            
            // 返回基础报告
            return this.generateFallbackReport(taskInfo, executionResults, error);
        }
    }

    /**
     * 同步报告到工单系统
     */
    async syncReportToWorkorder(ticketId, report) {
        try {
            this.logger.info(`🔄 ${this.agentName}同步报告到工单 ${ticketId}...`);
            
            const workorderUpdate = report.workorderUpdate;
            const updateData = {
                status: workorderUpdate.status,
                notes: workorderUpdate.notes,
                report: workorderUpdate.reportSummary,
                execution_details: {
                    completion_percentage: report.reportSummary.completionPercentage,
                    execution_time: report.reportSummary.executionTime,
                    steps_completed: report.executionDetails.completedSteps,
                    steps_total: report.executionDetails.totalSteps,
                    verification_result: report.verificationResults.finalVerification,
                    business_impact: report.businessImpact.businessResult
                }
            };
            
            // 更新工单状态
            const response = await axios.patch(
                `${this.workorderApiUrl}/api/tickets/${ticketId}/status`,
                updateData
            );
            
            this.logger.info(`✅ ${this.agentName}工单状态同步成功`);
            this.logger.info(`📋 工单状态: ${workorderUpdate.status}`);
            
            return {
                success: true,
                updatedTicket: response.data
            };
            
        } catch (error) {
            this.logger.error(`❌ ${this.agentName}工单同步失败:`, error);
            
            // 尝试基础状态更新
            try {
                await axios.patch(
                    `${this.workorderApiUrl}/api/tickets/${ticketId}/status`,
                    {
                        status: '处理失败',
                        notes: `汇报Agent同步失败: ${error.message}`
                    }
                );
                
                return {
                    success: false,
                    error: error.message,
                    fallbackUpdated: true
                };
            } catch (fallbackError) {
                this.logger.error(`❌ ${this.agentName}备用状态更新也失败:`, fallbackError);
                return {
                    success: false,
                    error: error.message,
                    fallbackUpdated: false
                };
            }
        }
    }

    /**
     * 生成富文本格式的报告摘要
     */
    async generateRichTextSummary(report) {
        try {
            this.logger.info(`📄 ${this.agentName}生成富文本报告摘要...`);
            
            const prompt = `
请根据以下执行报告，生成一份用户友好的富文本摘要，用于在工单系统中展示。

执行报告：
${JSON.stringify(report, null, 2)}

请生成HTML格式的报告摘要：
- 使用清晰的标题和段落结构
- 突出显示关键信息（成功/失败状态、完成度等）
- 包含业务影响和技术细节
- 提供可操作的建议

格式要求：
- 使用HTML标签（h3, p, ul, li, strong, em等）
- 适合在工单系统中显示
- 长度控制在500字以内

重要：请只返回HTML内容，不要包含markdown标记。`;

            const response = await this.modelClient.callMainModel(prompt);
            const richTextSummary = response.trim();
            
            this.logger.info(`✅ ${this.agentName}富文本摘要生成完成`);
            
            return richTextSummary;
            
        } catch (error) {
            this.logger.error(`❌ ${this.agentName}富文本摘要生成失败:`, error);
            
            // 返回基础HTML摘要
            return this.generateBasicHtmlSummary(report);
        }
    }

    /**
     * 发送实时状态更新
     */
    async sendRealtimeUpdate(ticketId, updateType, message, data = {}) {
        try {
            const updatePayload = {
                ticketId: ticketId,
                updateType: updateType, // 'progress', 'completion', 'error', 'verification'
                message: message,
                timestamp: new Date().toISOString(),
                agent: this.agentName,
                data: data
            };
            
            // 通过WebSocket发送实时更新（如果连接可用）
            // 这里可以集成WebSocket客户端
            
            this.logger.info(`📡 ${this.agentName}发送实时更新: ${updateType} - ${message}`);
            
            return true;
            
        } catch (error) {
            this.logger.error(`❌ ${this.agentName}实时更新发送失败:`, error);
            return false;
        }
    }

    /**
     * 生成备用报告
     */
    generateFallbackReport(taskInfo, executionResults, error) {
        return {
            reportSummary: {
                taskTitle: taskInfo.title || taskInfo.description || '未知任务',
                executionStatus: '失败',
                completionPercentage: 0,
                executionTime: '未知',
                finalResult: `报告生成失败: ${error.message}`
            },
            taskAnalysis: {
                originalObjective: taskInfo.objective || '未知目标',
                targetStore: '未识别',
                targetProduct: '未识别',
                expectedAction: '未知操作'
            },
            executionDetails: {
                totalSteps: 0,
                completedSteps: 0,
                failedSteps: 1,
                keyOperations: []
            },
            verificationResults: {
                verificationMethod: '无法验证',
                evidenceFound: [],
                evidenceMissing: ['报告生成失败'],
                finalVerification: '验证失败'
            },
            workorderUpdate: {
                status: '处理失败',
                notes: `汇报Agent报告生成失败: ${error.message}`,
                reportSummary: '执行报告生成异常，请人工检查'
            },
            metadata: {
                generatedAt: new Date().toLocaleString('zh-CN'),
                agentVersion: 'v3.0',
                reportId: `RPT-FALLBACK-${Date.now()}`,
                architecture: 'AI-First Multi-Agent RPA'
            }
        };
    }

    /**
     * 生成基础HTML摘要
     */
    generateBasicHtmlSummary(report) {
        const status = report.reportSummary.executionStatus;
        const statusIcon = status === '成功' ? '✅' : status === '失败' ? '❌' : '⚠️';
        
        return `
<h3>${statusIcon} RPA任务执行报告</h3>
<p><strong>任务:</strong> ${report.reportSummary.taskTitle}</p>
<p><strong>状态:</strong> ${report.reportSummary.executionStatus}</p>
<p><strong>完成度:</strong> ${report.reportSummary.completionPercentage}%</p>
<p><strong>执行时间:</strong> ${report.reportSummary.executionTime}</p>
<p><strong>最终结果:</strong> ${report.reportSummary.finalResult}</p>
<p><em>报告生成时间: ${report.metadata.generatedAt}</em></p>
        `.trim();
    }

    /**
     * 清理JSON响应
     */
    cleanJsonResponse(response) {
        try {
            let cleaned = response.trim();
            cleaned = cleaned.replace(/^```(?:json)?\s*\n?/i, '');
            cleaned = cleaned.replace(/\n?\s*```\s*$/i, '');
            const jsonMatch = cleaned.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                cleaned = jsonMatch[0];
            }
            return cleaned.replace(/\n\s*\n/g, '\n').trim();
        } catch (error) {
            this.logger.warn('⚠️ 清理JSON响应失败，使用原始响应:', error);
            return response;
        }
    }
}

module.exports = ReportingAgent;
