const express = require('express');
const WebSocket = require('ws');
const axios = require('axios');
require('dotenv').config({ path: '../../.env' });

// 使用原来的V1.0任务处理器
const TaskProcessor = require('./src/task-processor');
const TicketPoller = require('./src/ticket-poller');
const logger = require('./src/utils/logger');

class AIAssistantServer {
    constructor() {
        this.app = express();
        this.port = process.env.PORT || 3002;
        this.workorderApiUrl = process.env.WORKORDER_API_URL || 'http://localhost:3001';
        this.workorderWsUrl = process.env.WORKORDER_WS_URL || 'ws://localhost:3001';
        
        this.taskProcessor = new TaskProcessor();
        this.ticketPoller = new TicketPoller(this.taskProcessor, this.workorderApiUrl);
        this.ws = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 10;
        this.heartbeatInterval = null;
        this.reconnectTimeout = null;
        this.processingTickets = new Set(); // 正在处理的工单ID集合，避免重复处理

        this.setupExpress();
        this.connectToWorkorderSystem();

        // 启动工单轮询器作为备用方案
        setTimeout(() => {
            logger.info('🔄 启动工单轮询器作为WebSocket的备用方案');
            this.ticketPoller.start();
        }, 5000);
    }

    setupExpress() {
        this.app.use(express.json());

        // CORS中间件
        this.app.use((req, res, next) => {
            res.header('Access-Control-Allow-Origin', '*');
            res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
            res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');

            if (req.method === 'OPTIONS') {
                res.sendStatus(200);
            } else {
                next();
            }
        });

        // 健康检查
        this.app.get('/health', (req, res) => {
            res.json({
                status: 'ok',
                connected: this.isConnected,
                timestamp: new Date().toISOString()
            });
        });

        // 状态查询
        this.app.get('/status', (req, res) => {
            const pollerStatus = this.ticketPoller.getStatus();
            const processorStatus = this.taskProcessor.getStatus();
            res.json({
                success: true,
                data: {
                    status: this.isConnected ? 'online' : 'offline',
                    architecture: 'LangGraph RPA系统',
                    version: 'v2.0-LangGraph',
                    aiModel: 'qwen-turbo',
                    automation: 'Playwright + LangGraph工作流',
                    features: [
                        'LangGraph状态管理',
                        'PDCA循环执行',
                        '智能恢复机制',
                        '工作流节点化处理',
                        '状态持久化'
                    ],
                    workers: 1,
                    queue_size: this.taskProcessor.getQueueSize(),
                    processedTasks: this.taskProcessor.getProcessedCount(),
                    initialized: this.taskProcessor.isInitialized,
                    processor: processorStatus,
                    uptime: process.uptime(),
                    last_activity: new Date().toISOString(),
                    poller: {
                        active: pollerStatus.isPolling,
                        interval: pollerStatus.pollingInterval,
                        processedTickets: pollerStatus.processedCount
                    }
                }
            });
        });

        // 获取最近任务
        this.app.get('/recent-tasks', (req, res) => {
            try {
                const limit = parseInt(req.query.limit) || 5;
                const processorStatus = this.taskProcessor.getStatus();

                // 获取真实的任务历史
                const recentTasks = this.taskProcessor.getRecentTasks(limit);

                res.json({
                    success: true,
                    data: recentTasks.map(task => ({
                        id: task.id,
                        title: task.title || '任务处理',
                        description: task.description || task.action || '执行中...',
                        status: task.status || 'processing',
                        timestamp: task.timestamp || new Date().toISOString(),
                        ticketId: task.ticketId,
                        iteration: task.iteration
                    }))
                });
            } catch (error) {
                logger.error('获取最近任务失败:', error);
                res.status(500).json({
                    success: false,
                    message: '获取最近任务失败',
                    error: error.message
                });
            }
        });

        // 启动AI助手
        this.app.post('/start', (req, res) => {
            try {
                if (this.isConnected) {
                    res.json({
                        success: true,
                        message: 'AI助手已经在运行',
                        status: 'online'
                    });
                } else {
                    // 尝试重新连接
                    this.connectToWorkorderSystem();
                    res.json({
                        success: true,
                        message: 'AI助手启动成功',
                        status: 'starting'
                    });
                }
            } catch (error) {
                logger.error('启动AI助手失败:', error);
                res.status(500).json({
                    success: false,
                    message: '启动AI助手失败',
                    error: error.message
                });
            }
        });

        // 停止AI助手
        this.app.post('/stop', (req, res) => {
            try {
                // 停止WebSocket连接
                if (this.ws) {
                    this.ws.close();
                }

                // 停止工单轮询器
                this.ticketPoller.stop();

                // 清除心跳定时器
                if (this.heartbeatInterval) {
                    clearInterval(this.heartbeatInterval);
                    this.heartbeatInterval = null;
                }

                // 清除重连定时器
                if (this.reconnectTimeout) {
                    clearTimeout(this.reconnectTimeout);
                    this.reconnectTimeout = null;
                }

                // 重置连接状态
                this.isConnected = false;
                this.reconnectAttempts = 0;

                // 清空正在处理的工单
                this.processingTickets.clear();

                logger.info('🛑 AI助手已停止工作，但服务器保持运行');

                res.json({
                    success: true,
                    message: 'AI助手已停止',
                    status: 'offline'
                });

                // 注意：不再关闭服务器进程，保持服务器运行以便重新启动
            } catch (error) {
                logger.error('停止AI助手失败:', error);
                res.status(500).json({
                    success: false,
                    message: '停止AI助手失败',
                    error: error.message
                });
            }
        });

        // 获取最近任务
        this.app.get('/recent-tasks', (req, res) => {
            try {
                const limit = parseInt(req.query.limit) || 5;
                const pollerStatus = this.ticketPoller.getStatus();

                // 模拟最近任务数据
                const recentTasks = [];
                for (let i = 0; i < Math.min(limit, pollerStatus.processedCount); i++) {
                    recentTasks.push({
                        id: `task_${Date.now()}_${i}`,
                        title: `AI-First RPA任务 ${i + 1}`,
                        status: 'completed',
                        timestamp: new Date(Date.now() - i * 60000).toISOString(),
                        duration: Math.floor(Math.random() * 300) + 30 // 30-330秒
                    });
                }

                res.json({
                    success: true,
                    data: recentTasks,
                    total: pollerStatus.processedCount
                });
            } catch (error) {
                logger.error('获取最近任务失败:', error);
                res.status(500).json({
                    success: false,
                    message: '获取最近任务失败',
                    error: error.message
                });
            }
        });

        this.server = this.app.listen(this.port, () => {
            logger.info(`AI助手服务启动: http://localhost:${this.port}`);
        });
    }

    connectToWorkorderSystem() {
        // 避免重复连接
        if (this.ws && (this.ws.readyState === WebSocket.CONNECTING || this.ws.readyState === WebSocket.OPEN)) {
            return;
        }

        logger.info('连接到工单系统...');

        try {
            // 修复WebSocket连接URL - 确保正确的格式
            const baseUrl = this.workorderWsUrl.replace('ws://', '').replace('wss://', '');
            const wsUrl = `ws://${baseUrl}/ws?type=ai-assistant&clientId=ai-assistant-${Date.now()}`;

            logger.info(`🔗 WebSocket连接URL: ${wsUrl}`);
            this.ws = new WebSocket(wsUrl);

            this.ws.on('open', () => {
                logger.info('✅ WebSocket连接已建立');
                this.isConnected = true;
                this.reconnectAttempts = 0; // 重置重连计数

                // 发送注册消息
                this.ws.send(JSON.stringify({
                    type: 'register',
                    clientType: 'ai-assistant',
                    timestamp: new Date().toISOString()
                }));

                // 订阅工单事件
                this.ws.send(JSON.stringify({
                    type: 'subscribe_tickets',
                    timestamp: new Date().toISOString()
                }));

                // 停止轮询器，使用WebSocket推送
                if (this.ticketPoller.isPolling) {
                    logger.info('🔄 WebSocket连接成功，停止轮询器');
                    this.ticketPoller.stop();
                }

                // 启动心跳
                this.startHeartbeat();
            });

            this.ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    this.handleWorkorderMessage(message);
                } catch (error) {
                    logger.error('解析WebSocket消息失败:', error);
                }
            });

            this.ws.on('close', (code, reason) => {
                logger.warn(`❌ WebSocket连接断开 (${code}): ${reason}`);
                this.isConnected = false;
                this.stopHeartbeat();

                // 启动轮询器作为备用
                if (!this.ticketPoller.isPolling) {
                    this.ticketPoller.start();
                }

                // 智能重连：指数退避
                this.scheduleReconnect();
            });

            this.ws.on('error', (error) => {
                logger.error('❌ WebSocket错误:', error.message || error);
                this.isConnected = false;

                // 启动轮询器作为备用
                if (!this.ticketPoller.isPolling) {
                    logger.info('🔄 WebSocket错误，启动轮询器作为备用');
                    this.ticketPoller.start();
                }
            });

        } catch (error) {
            logger.error('❌ WebSocket连接创建失败:', error);

            // 启动轮询器作为备用
            if (!this.ticketPoller.isPolling) {
                this.ticketPoller.start();
            }
        }
    }

    /**
     * 启动心跳机制
     */
    startHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
        }

        this.heartbeatInterval = setInterval(() => {
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                this.ws.send(JSON.stringify({
                    type: 'heartbeat',
                    timestamp: new Date().toISOString()
                }));
            }
        }, 30000); // 每30秒发送一次心跳
    }

    /**
     * 停止心跳机制
     */
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    /**
     * 智能重连调度
     */
    scheduleReconnect() {
        if (this.reconnectTimeout) {
            clearTimeout(this.reconnectTimeout);
        }

        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            logger.error('❌ WebSocket重连次数超过限制，停止重连');
            return;
        }

        this.reconnectAttempts++;

        // 指数退避：2^attempts * 1000ms，最大30秒
        const delay = Math.min(Math.pow(2, this.reconnectAttempts) * 1000, 30000);

        logger.info(`🔄 ${delay/1000}秒后尝试第${this.reconnectAttempts}次重连...`);

        this.reconnectTimeout = setTimeout(() => {
            this.connectToWorkorderSystem();
        }, delay);
    }

    async handleWorkorderMessage(message) {
        switch (message.type) {
            case 'connection_established':
                logger.info('✅ WebSocket连接确认');
                break;

            case 'register_confirmed':
                logger.info('✅ AI助手注册成功');
                break;

            case 'subscription_confirmed':
                logger.info('✅ 工单事件订阅成功');
                break;

            case 'ticket_created':
            case 'new_ticket_available':
            case 'ticket_status_changed':
            case 'ticket_updated':
                const ticketId = message.data?.id || message.ticketId || message.id;
                const status = message.data?.status || message.status;

                // 处理挂起操作
                if (status === '已挂起') {
                    logger.info(`🛑 收到工单挂起通知: ${ticketId}`);
                    await this.handleTicketSuspended(ticketId);
                    break;
                }

                // 只处理待开始或排队中的工单
                if (status === '待开始' || status === '排队中' || !status) {
                    logger.info(`📨 收到新工单推送: ${ticketId}`);
                    await this.processNewTicket(ticketId);
                }
                break;

            case 'ping_request':
                // 响应服务端心跳请求
                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                    this.ws.send(JSON.stringify({
                        type: 'heartbeat',
                        timestamp: new Date().toISOString()
                    }));
                }
                break;

            case 'heartbeat_response':
                logger.debug('💓 收到心跳响应');
                break;

            default:
                logger.debug(`收到未处理的消息类型: ${message.type}`);
                break;
        }
    }

    async processNewTicket(ticketId) {
        try {
            // 检查是否已经在处理中，避免重复处理
            if (this.processingTickets.has(ticketId)) {
                logger.info(`工单 ${ticketId} 已在处理中，跳过重复处理`);
                return;
            }

            // 标记为处理中
            this.processingTickets.add(ticketId);

            // 获取工单详情
            const response = await axios.get(`${this.workorderApiUrl}/api/tickets/${ticketId}`);
            const ticket = response.data.data || response.data;

            if (ticket.status !== '待开始' && ticket.status !== '排队中') {
                logger.info(`工单 ${ticketId} 状态为 ${ticket.status}，跳过处理`);
                this.processingTickets.delete(ticketId); // 移除处理标记
                return;
            }

            logger.info(`🎯 开始LangGraph RPA处理工单: ${ticket.title}`);

            // 更新工单状态为处理中
            await axios.patch(`${this.workorderApiUrl}/api/tickets/${ticketId}/status`, {
                status: '处理中',
                notes: 'LangGraph RPA系统开始处理工单 (LangGraph + qwen AI + Playwright)'
            });

            // 使用LangGraph RPA处理工单
            const result = await this.taskProcessor.processTicket(ticket);

            if (result.success) {
                logger.info(`✅ LangGraph RPA工单处理成功: ${ticketId}`);
                logger.info(`📊 执行统计: ${result.stepsCompleted}步骤, ${(result.executionTime/1000).toFixed(2)}秒`);
            } else {
                logger.error(`❌ LangGraph RPA工单处理失败: ${ticketId} - ${result.error}`);
            }

        } catch (error) {
            logger.error(`❌ LangGraph RPA工单处理异常 ${ticketId}:`, error);

            // 更新工单状态为失败
            try {
                await axios.patch(`${this.workorderApiUrl}/api/tickets/${ticketId}/status`, {
                    status: '处理失败',
                    notes: `LangGraph RPA系统异常: ${error.message}`
                });
            } catch (updateError) {
                logger.error('更新工单状态失败:', updateError);
            }
        } finally {
            // 无论成功还是失败，都要移除处理标记
            this.processingTickets.delete(ticketId);
            logger.debug(`🧹 清理工单 ${ticketId} 的处理标记`);
        }
    }

    /**
     * 处理工单挂起
     */
    async handleTicketSuspended(ticketId) {
        try {
            logger.info(`🛑 处理工单挂起: ${ticketId}`);

            // 如果工单正在处理中，停止处理
            if (this.processingTickets.has(ticketId)) {
                logger.info(`⏹️ 停止正在处理的工单: ${ticketId}`);

                // 通知任务处理器停止处理该工单
                if (this.taskProcessor) {
                    await this.taskProcessor.suspendTicket(ticketId);
                }

                // 移除处理标记
                this.processingTickets.delete(ticketId);

                logger.info(`✅ 工单 ${ticketId} 已成功挂起`);
            } else {
                logger.info(`ℹ️ 工单 ${ticketId} 未在处理中，无需停止`);
            }

        } catch (error) {
            logger.error(`❌ 处理工单挂起失败 ${ticketId}:`, error);
        }
    }
}

// 启动服务
logger.info('🚀 启动LangGraph版AI-First RPA系统 v2.0...');
logger.info('🎯 架构: LangGraph工作流 + qwen AI + Playwright');
logger.info('⚡ 特性: 状态管理、PDCA循环、智能恢复、节点化处理');
logger.info('🔍 核心优势: 工作流状态持久化，支持挂起恢复和错误处理');
new AIAssistantServer();
