const axios = require('axios');
require('dotenv').config();

/**
 * qwen-turbo vs qwen-plus 对比测试
 * 测试任务：下架小泉居(威翠店)的咖哩焗魚飯
 */

const testPrompt = `
你是一个专业的Web自动化专家。现在需要你分析以下任务并制定详细的执行计划。

任务内容：下架小泉居(威翠店)的咖哩焗魚飯

系统指南：
# 管理后台地址
BD商户后台:
- 主地址:(https://uat-merchant.aomiapp.com/#/bdlogin)
- 备选地址:(https://uat-merchant.aomiapp.com/#/select?noDirect=1)

## 登录流程和页面状态说明
1. **登录页面** (https://uat-merchant.aomiapp.com/#/bdlogin)
   - 如果用户未登录，会显示登录表单
     - 请将信息反馈到工单系统,要求用户帮忙登录
   - 如果用户已登录，会自动跳转到选择页面

2. **选择页面** (https://uat-merchant.aomiapp.com/#/select)
   - 登录成功后会跳转到此页面
   - 页面显示门店选择界面，包含门店输入框和查询按钮
   - 这是正常的登录后状态，不需要返回登录页面

3. **页面跳转逻辑**
   - 从 /bdlogin 登录成功后 → 自动跳转到 /select
   - 在 /select 页面输入门店信息并查询 → 进入具体的商户管理后台
   - 如果发现页面URL是 /select，说明已经登录成功，应该继续后续操作

## 下架门店外卖商品
1. 打开BD商户后台；
2. 如需登录,请要求用户帮忙登录,登录成功后才继续；
3. 在<门店输入框输>入完整门店 id 或门店名称关键词筛选门店,点击目标门店,并点击<查询>进入商户管理后台;
4. 如点击<查询>没有进入商户管理后台,则点击页面上的相应门店名称,即可进入;
5. 在商户管理后台点击左侧菜单的<系统管理>展开菜单,点击<门店管理>,显示该商户下的门店列表;
6. 在门店列表点击目标门店的<进入门店>按钮,进入门店管理后台(页面顶部会显示门店名称);
7. 在门店管理后台点击左侧菜单的<商品管理>展开菜单,点击<外卖商品管理>,显示外卖商品列表;
8. 在外卖商品列表的搜索框,根据需要,选择搜索类型{商品名称, 商品 id},然后输入关键词,并点击<🔍>搜索;
9. 在搜索列表滚动查找目标商品,找到后点击<下架>,二次确认后即可下架;
10. 找到所有该下架的商品并操作下架后,即完成操作;

请你详细分析这个任务，并制定完整的执行计划。特别注意：
1. 门店选择的重要性
2. 系统管理→门店管理的步骤
3. 进入具体门店后台的步骤
4. 商品管理→外卖商品管理的步骤

请按照以下格式回答：
1. 任务理解
2. 关键步骤识别
3. 详细执行计划
4. 可能遇到的问题和解决方案
`;

async function callQwenAPI(model, prompt) {
    try {
        const response = await axios.post('https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions', {
            model: model,
            messages: [
                {
                    role: 'user',
                    content: prompt
                }
            ],
            temperature: 0.1,
            max_tokens: 2000
        }, {
            headers: {
                'Authorization': `Bearer ${process.env.DASHSCOPE_API_KEY}`,
                'Content-Type': 'application/json'
            }
        });

        return {
            success: true,
            model: model,
            response: response.data.choices[0].message.content,
            usage: response.data.usage
        };
    } catch (error) {
        return {
            success: false,
            model: model,
            error: error.response?.data || error.message
        };
    }
}

async function runComparison() {
    console.log('🧪 开始 qwen-turbo vs qwen-plus 对比测试...\n');
    console.log('📋 测试任务: 下架小泉居(威翠店)的咖哩焗魚飯\n');
    console.log('=' .repeat(80));

    // 测试 qwen-turbo
    console.log('\n🔵 测试 qwen-turbo...');
    const turboResult = await callQwenAPI('qwen-turbo', testPrompt);
    
    // 测试 qwen-plus
    console.log('\n🟢 测试 qwen-plus...');
    const plusResult = await callQwenAPI('qwen-plus', testPrompt);

    // 输出结果
    console.log('\n' + '=' .repeat(80));
    console.log('📊 测试结果对比');
    console.log('=' .repeat(80));

    if (turboResult.success) {
        console.log('\n🔵 qwen-turbo 回答:');
        console.log('-' .repeat(40));
        console.log(turboResult.response);
        console.log('\n📈 Token使用:', turboResult.usage);
    } else {
        console.log('\n❌ qwen-turbo 调用失败:', turboResult.error);
    }

    console.log('\n' + '=' .repeat(80));

    if (plusResult.success) {
        console.log('\n🟢 qwen-plus 回答:');
        console.log('-' .repeat(40));
        console.log(plusResult.response);
        console.log('\n📈 Token使用:', plusResult.usage);
    } else {
        console.log('\n❌ qwen-plus 调用失败:', plusResult.error);
    }

    // 分析对比
    console.log('\n' + '=' .repeat(80));
    console.log('🔍 关键差异分析');
    console.log('=' .repeat(80));

    if (turboResult.success && plusResult.success) {
        console.log('\n📋 检查关键步骤理解:');
        
        const keySteps = [
            '门店输入框',
            '门店选择',
            '系统管理',
            '门店管理',
            '进入门店',
            '商品管理',
            '外卖商品管理'
        ];

        keySteps.forEach(step => {
            const turboHas = turboResult.response.includes(step);
            const plusHas = plusResult.response.includes(step);
            
            console.log(`${step}: turbo(${turboHas ? '✅' : '❌'}) vs plus(${plusHas ? '✅' : '❌'})`);
        });

        console.log('\n📊 回答长度对比:');
        console.log(`qwen-turbo: ${turboResult.response.length} 字符`);
        console.log(`qwen-plus: ${plusResult.response.length} 字符`);

        console.log('\n🎯 详细程度对比:');
        const turboSteps = (turboResult.response.match(/\d+\./g) || []).length;
        const plusSteps = (plusResult.response.match(/\d+\./g) || []).length;
        console.log(`qwen-turbo: ${turboSteps} 个步骤`);
        console.log(`qwen-plus: ${plusSteps} 个步骤`);
    }

    console.log('\n✅ 对比测试完成!');
}

// 运行测试
runComparison().catch(console.error);
