#!/usr/bin/env node

/**
 * 创建测试工单 - 修复API配置后的测试
 */

const axios = require('axios');

async function createTestWorkorder() {
    try {
        console.log('🎯 创建测试工单...');
        
        const workorder = {
            title: '下架小泉居(威翠店)的咖哩焗魚飯',
            content: `我要下架小泉居(威翠店)的咖哩焗魚飯

请按照以下步骤操作：
1. 打开BD商户后台：https://uat-merchant.aomiapp.com/#/bdlogin
2. 如需登录，请要求用户帮忙登录，登录成功后才继续
3. 在门店输入框输入"小泉居"或"威翠店"筛选门店，点击目标门店，并点击查询进入商户管理后台
4. 点击左侧菜单的系统管理→门店管理，显示该商户下的门店列表
5. 在门店列表点击威翠店的进入门店按钮，进入门店管理后台
6. 点击左侧菜单的商品管理→外卖商品管理，显示外卖商品列表
7. 在外卖商品列表的搜索框，选择商品名称，输入"咖哩焗魚飯"，点击🔍搜索
8. 在搜索列表找到咖哩焗魚飯商品，点击下架，二次确认后完成下架
9. 确认下架成功

请严格按照system_guide.md中的操作指引完成此任务。`,
            status: '待开始',
            priority: 'high'
        };

        const response = await axios.post('http://localhost:3001/api/tickets', workorder);
        
        console.log('✅ 测试工单创建成功!');
        console.log('📋 工单ID:', response.data.id);
        console.log('📝 工单标题:', response.data.title);
        console.log('📊 工单状态:', response.data.status);
        console.log('⏰ 创建时间:', response.data.created_at);
        
        console.log('\n🤖 AI助手应该会自动开始处理这个工单...');
        
    } catch (error) {
        console.error('❌ 创建工单失败:', error.message);
        if (error.response) {
            console.error('📋 响应状态:', error.response.status);
            console.error('📋 响应数据:', error.response.data);
        }
    }
}

createTestWorkorder();
