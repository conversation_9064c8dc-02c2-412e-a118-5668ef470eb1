{"name": "rpa-automation-platform", "version": "1.0.0", "description": "RPA自动化平台 - 通过AI助手实现管理后台操作的自动化", "main": "index.js", "scripts": {"install:all": "npm install && npm run install:workorder && npm run install:ai", "install:workorder": "cd src/workorder-system/backend && npm install && cd ../frontend && npm install", "install:ai": "cd src/ai-assistant && npm install", "dev": "concurrently \"npm run dev:workorder-backend\" \"npm run dev:workorder-frontend\" \"npm run dev:ai-assistant\"", "dev:workorder-backend": "cd src/workorder-system/backend && npm run dev", "dev:workorder-frontend": "cd src/workorder-system/frontend && npm run dev", "dev:ai-assistant": "cd src/ai-assistant && npm run dev", "build": "npm run build:workorder && npm run build:ai", "build:workorder": "cd src/workorder-system/frontend && npm run build", "build:ai": "cd src/ai-assistant && npm run build", "start": "concurrently \"npm run start:workorder\" \"npm run start:ai\"", "start:workorder": "cd src/workorder-system/backend && npm start", "start:ai": "cd src/ai-assistant && npm start", "test": "npm run test:workorder && npm run test:ai", "test:workorder": "cd src/workorder-system && npm test", "test:ai": "cd src/ai-assistant && npm test", "test:ai-quick": "node test-ai-assistant.js", "test:ai-full": "cd src/ai-assistant && node run-tests.js", "setup": "node scripts/setup.js", "db:init": "node scripts/init-database.js", "db:seed": "node scripts/seed-database.js"}, "keywords": ["rpa", "automation", "ai", "workflow", "playwright", "vue", "nodejs"], "author": "RPA Team", "license": "MIT", "devDependencies": {"concurrently": "^7.6.0", "eslint": "^8.57.0", "prettier": "^3.2.5"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-org/rpa-automation-platform.git"}, "bugs": {"url": "https://github.com/your-org/rpa-automation-platform/issues"}, "homepage": "https://github.com/your-org/rpa-automation-platform#readme", "dependencies": {"@vueup/vue-quill": "^1.2.0", "axios": "^1.10.0", "dotenv": "^17.0.1", "eventsource": "^4.0.0", "playwright": "^1.40.0", "quill": "^2.0.3", "sqlite3": "^5.1.7", "ws": "^8.18.3"}}