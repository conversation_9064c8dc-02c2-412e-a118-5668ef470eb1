# RPA自动化平台环境变量配置

# 数据库配置
DATABASE_PATH=./src/ai-assistant/data/database.db

# 大模型配置
DASHSCOPE_API_KEY=sk-7416a845a80443db82afc35951e804e7
DASHSCOPE_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
MAIN_MODEL=qwen-plus
VL_MODEL=qwen-vl-plus

# 服务端口配置
WORKORDER_BACKEND_PORT=3001
WORKORDER_FRONTEND_PORT=3000
AI_ASSISTANT_PORT=3002

# WebSocket配置
WEBSOCKET_PORT=3003

# 并发配置
MAX_CONCURRENT_WORKERS=1
MIN_CONCURRENT_WORKERS=1
BROWSER_POOL_SIZE=8
WORKER_IDLE_TIMEOUT=300000
HEARTBEAT_INTERVAL=30000

# 文件存储配置
SCREENSHOT_DIR=./src/ai-assistant/data/screenshots
LOG_DIR=./src/ai-assistant/data/logs
TEMP_DIR=./src/ai-assistant/data/temp

# 开发模式配置
NODE_ENV=development
LOG_LEVEL=debug

# 浏览器配置
BROWSER_HEADLESS=false
BROWSER_TIMEOUT=30000
PAGE_LOAD_TIMEOUT=30000

# 图片处理配置
MAX_IMAGE_WIDTH=800
MAX_IMAGE_HEIGHT=600
IMAGE_QUALITY=80

# 安全配置
JWT_SECRET=your-jwt-secret-key-here
SESSION_SECRET=your-session-secret-key-here

# CORS配置
CORS_ORIGIN=http://localhost:3000

# 重试配置
MAX_RETRIES=3
RETRY_DELAY=1000

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=3004